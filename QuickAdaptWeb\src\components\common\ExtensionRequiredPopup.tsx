import React from 'react';
import { Button, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
interface ExtensionRequiredPopupProps {
  setShowPopup: (show: boolean) => void;
  showPopup: boolean;
  name: string;
  setShowInput?: (show: boolean) => void;
}

const ExtensionRequiredPopup: React.FC<ExtensionRequiredPopupProps> = ({
  setShowPopup,
  showPopup,
  name,
  setShowInput
}) => {
  const { t: translate } = useTranslation();
  const handleInstallExtension = () => {
    // Open Chrome Web Store to install the extension
    window.open('https://chromewebstore.google.com/detail/quickadapt/bniabjfpljchdaipkcadcnpjndjdoaom', '_blank');
  };

  return (
    <div>
      {showPopup && (
        <div className="qadpt-createpopup">
          <div className="qadpt-title-sec">
            <div className="qadpt-title">{translate("Extension Required")}</div>
            {/* <div className="qadpt-sub-title">
              To create a new {name}, you need to install the QuickAdapt Chrome Extension
            </div> */}
            <svg
              onClick={() => {
                setShowPopup(false);
                if (setShowInput) {
                  setShowInput(false);
                }
              }}
              className="qadpt-closeicon"
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 50 50"
            >
              <path d="M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z" />
            </svg>
          </div>
          <div className="qadpt-createflds" style={{ textAlign: 'center', padding: '20px' }}>
            <Typography variant="body1" style={{ marginBottom: '20px' }}>
              `{translate("The QuickAdapt Chrome Extension is required to create and manage ")} {translate(name)} {translate("on your website.")}`
            </Typography>
            <Typography variant="body2" style={{ marginBottom: '30px' }}>
              {translate("After installing the extension, please refresh this page.")}
            </Typography>
          </div>
          <div className="qadpt-save-btn" style={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
            <Button
              variant="contained"
              onClick={handleInstallExtension}
              color="primary"
              style={{ minWidth: '150px' }}
            >
              {translate("Install Extension")}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExtensionRequiredPopup;
