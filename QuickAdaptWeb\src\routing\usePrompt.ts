import { useEffect, useRef, useState, useCallback } from "react";
import { useLocation, useNavigate } from "react-router-dom";

export function usePrompt(when: boolean) {
  const location = useLocation();
  const navigate = useNavigate();
  const [showPrompt, setShowPrompt] = useState(false);
  const [pendingLocation, setPendingLocation] = useState<string | null>(null);
  const allowTransition = useRef(false);
  const currentPath = useRef(location.pathname);
  const isBlocking = useRef(false);

  // Initialize current path on mount
  useEffect(() => {
    currentPath.current = location.pathname;
  }, [location.pathname]);

  // Track location changes and block if necessary
  useEffect(() => {
    // If we're not supposed to block, just update the current path
    if (!when) {
      currentPath.current = location.pathname;
      return;
    }

    // If location changed
    if (location.pathname !== currentPath.current) {
      // If we're allowing this transition, update and continue
      if (allowTransition.current) {
        allowTransition.current = false;
        currentPath.current = location.pathname;
        isBlocking.current = false;
        return;
      }

      // If we're already in the process of blocking, don't show prompt again
      if (isBlocking.current) {
        return;
      }

      // We need to block this navigation
      isBlocking.current = true;
      setPendingLocation(location.pathname);
      setShowPrompt(true);

      // Navigate back to the current path to block the navigation
      setTimeout(() => {
        navigate(currentPath.current, { replace: true });
      }, 0);
    }
  }, [location.pathname, when, navigate]);

  // Handle browser back/forward and refresh
  useEffect(() => {
    if (!when) return;

    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      event.preventDefault();
      event.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
      return event.returnValue;
    };

    const handlePopState = (event: PopStateEvent) => {
      if (!allowTransition.current && !isBlocking.current) {
        event.preventDefault();
        isBlocking.current = true;
        
        // Push current state back
        window.history.pushState(null, '', currentPath.current);
        
        // Show the confirmation dialog
        setPendingLocation(window.location.pathname);
        setShowPrompt(true);
      }
    };

    // Override history methods to catch programmatic navigation
    const originalPushState = window.history.pushState;
    const originalReplaceState = window.history.replaceState;

    window.history.pushState = function(state, title, url) {
      if (when && !allowTransition.current && !isBlocking.current && url) {
        const targetPath = typeof url === 'string' ? url : url.toString();
        const newPath = targetPath.startsWith('/') ? targetPath : new URL(targetPath, window.location.origin).pathname;
        
        if (newPath !== currentPath.current) {
          isBlocking.current = true;
          setPendingLocation(newPath);
          setShowPrompt(true);
          return;
        }
      }
      return originalPushState.call(this, state, title, url);
    };

    window.history.replaceState = function(state, title, url) {
      if (when && !allowTransition.current && !isBlocking.current && url) {
        const targetPath = typeof url === 'string' ? url : url.toString();
        const newPath = targetPath.startsWith('/') ? targetPath : new URL(targetPath, window.location.origin).pathname;
        
        if (newPath !== currentPath.current) {
          isBlocking.current = true;
          setPendingLocation(newPath);
          setShowPrompt(true);
          return;
        }
      }
      return originalReplaceState.call(this, state, title, url);
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('popstate', handlePopState);
      window.history.pushState = originalPushState;
      window.history.replaceState = originalReplaceState;
    };
  }, [when]);

  const confirmNavigation = useCallback(() => {
    setShowPrompt(false);
    allowTransition.current = true;
    isBlocking.current = false;
    
    if (pendingLocation) {
      const targetPath = pendingLocation;
      setPendingLocation(null);
      
      // Navigate to the pending location
      setTimeout(() => {
        navigate(targetPath);
      }, 0);
    }
  }, [pendingLocation, navigate]);

  const cancelNavigation = useCallback(() => {
    setShowPrompt(false);
    setPendingLocation(null);
    isBlocking.current = false;
  }, []);

  return { showPrompt, confirmNavigation, cancelNavigation };
}
