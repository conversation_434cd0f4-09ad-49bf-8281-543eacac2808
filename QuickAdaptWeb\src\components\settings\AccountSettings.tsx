import React, { useState, useRef, useEffect } from 'react';
import { isSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import { translateText } from '../multilingual/Translator';
import { TextField, Button, Typography, Box, Tooltip } from "@mui/material";
import { useRtl } from "../../RtlContext";

const AccountSettings = ({ selectedLanguageProp }: { selectedLanguageProp: string }) => {
	const [editModeOne, setEditModeOne] = useState(false);
	const [editModeTwo, setEditModeTwo] = useState(false);
	const inputRefOne = useRef<HTMLInputElement>(null);
	const inputRefTwo = useRef<HTMLInputElement>(null);
	const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());
	const { isRtl } = useRtl();

	const useTranslation = (text: string) => {
		const [translatedText, setTranslatedText] = useState("");
		const fromLanguage = "en";

		useEffect(() => {
			const fetchTranslation = async () => {
				const translation = await translateText(text, fromLanguage, selectedLanguageProp);
				setTranslatedText(translation);
			};

			fetchTranslation();
		}, [text, fromLanguage, selectedLanguageProp]);

		return translatedText;
	};

	// Pre-fetch translations
	const accountNameText = useTranslation("Account name");
	const securityPrivacyText = useTranslation("Security & privacy notifications");
	const receiveNotificationsText = useTranslation(
		"Receive status, subprocessor, and other notifications to these comma-separated emails."
	);
	const viewText = useTranslation("View");
	const statusPageText = useTranslation("Status Page");
	const editText = useTranslation("Edit");
	const saveText = useTranslation("Save");
	const placeholderOne = useTranslation("Quixy");
	const placeholderTwo = useTranslation("e.g. <EMAIL>, <EMAIL>");
	const roleTooltipText = useTranslation("Your role doesn't allow you to do this");

	const toggleEditModeOne = () => {
		setEditModeOne(!editModeOne);
		if (!editModeOne && inputRefOne.current) {
			inputRefOne.current.focus();
		}
	};

	const toggleEditModeTwo = () => {
		setEditModeTwo(!editModeTwo);
		if (!editModeTwo && inputRefTwo.current) {
			inputRefTwo.current.focus();
		}
	};

	useEffect(() => {
		const unsubscribe = subscribe(setSidebarOpen);
		return () => unsubscribe();
	}, []);

	return (
		<Box
			sx={{
				marginLeft: sidebarOpen ? "190px" : "0",
				direction: isRtl ? "rtl" : "ltr",
				transition: "margin-left 0.3s",
				left: isRtl ? "-260px" : "",
				position: "relative",
				display: 'flex',
				justifyContent: 'center',
				
				minHeight: '100vh'
			}}
		>
			<Box
				sx={{
					width: '96%',
					backgroundColor: '#ffffff',
					padding: '20px',
					borderRadius: '8px',
					boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
					marginTop: '20px'
				}}
			>
				<Box className="account-block">
					<Typography
						variant="h6"
						sx={{ ml: 2 }}
					>
						{accountNameText}
					</Typography>
					<Box sx={{ display: "flex", alignItems: "center", width: "75%", position: "relative" }}>
						<TextField
							name="input"
							variant="outlined"
							placeholder={placeholderOne}
							sx={{
								ml: 2,
								flexGrow: 1,
								"& .MuiInputBase-root": {
									height: "40px", // Sets the height of the input
									boxSizing: "border-box",
								},
							}}
							disabled={!editModeOne}
							inputRef={inputRefOne}
						/>
						<Button
							variant="contained"
							sx={{ ml: 2 }}
							onClick={toggleEditModeOne}
							style={{
								width: "80px",
								left: isRtl ? "16px" : "-17px",
								height: "40px",
							}}
						>
							{editModeOne ? saveText : editText}
						</Button>
					</Box>
				</Box>

				<Box className="account-block">
					<Typography
						variant="h6"
						sx={{ ml: 2, mb: 1 }}
					>
						{securityPrivacyText}
					</Typography>
					<Typography
						variant="body2"
						sx={{ mb: 1 }}
					>
						{receiveNotificationsText} {viewText} <a href="/status-page">{statusPageText}</a>
					</Typography>
					<Box sx={{ display: "flex", alignItems: "center", width: "75%", position: "relative" }}>
						<TextField
							name="input"
							variant="outlined"
							placeholder={placeholderTwo}
							sx={{
								ml: 2,
								flexGrow: 1,
								"& .MuiInputBase-root": {
									height: "40px", // Sets the height of the input
									boxSizing: "border-box",
								},
							}}
							disabled={!editModeTwo}
							inputRef={inputRefTwo}
						/>
						<Tooltip
							title={roleTooltipText}
							placement="top"
							arrow
						>
							<Box sx={{ ml: 2 }}>
								<Button
									variant="contained"
									onClick={toggleEditModeTwo}
									disabled={!editModeTwo}
									style={{
										width: "80px",
										left: isRtl ? "16px" : "-17px",
										height: "40px",
									}}
								>
									{editModeTwo ? saveText : editText}
								</Button>
							</Box>
						</Tooltip>
					</Box>
				</Box>
			</Box>
		</Box>
	);
};

export default AccountSettings;
