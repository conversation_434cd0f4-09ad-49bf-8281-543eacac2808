import React, { useState, useEffect, useRef, startTransition, useContext } from "react";
import AccountCircleOutlinedIcon from '@mui/icons-material/AccountCircleOutlined';
import { useNavigate } from "react-router-dom";
import i18n from '../multilingual/i18n';
import { SAinitialsData } from "../login/Superadminloginpage";
import { Popover, Typography, Button, Avatar, Box } from '@mui/material';
import { initialsData } from "../auth/AuthProvider";
import { initialssData } from "../settings/ProfileSettings";
import SideMenu from "./sideMenu";
import { quickadopt } from "../../assets/icons/icons";
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import LogoutIcon from '@mui/icons-material/Logout';
import LocalActivityIcon from '@mui/icons-material/LocalActivity';
import Popup from "../common/Popup";
import { FormControl, InputLabel, InputAdornment, IconButton, MenuItem, Grid } from "@mui/material";
import Select from "@mui/material/Select";
import LanguageIcon from '@mui/icons-material/Language';
import { translateText } from "../multilingual/Translator";
import Translater from "../multilingual/Multilingual";
import { useLocation } from "react-router-dom";
import { LanguageProvider } from "../multilingual/LanguageContext";
import ProfileSettings from "../settings/ProfileSettings";
import Settings from "../settings/Settings";
import AccountSettings from "../settings/AccountSettings";
import { getLabels, getLanguages, updateLanguage } from "../../services/MultilingualService";
import { setSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import MenuOpenIcon from '@mui/icons-material/MenuOpen';
import MenuIcon from '@mui/icons-material/Menu';
import { Sidebar } from "../common/Sidebar";
import PageWrapper from "../pagewrapper";
import { Feedback, Logout, settings, logo, global, profile, ArrowDown } from "../../assets/icons/icons";
import { isSidebarOpen } from "../adminMenu/sidemenustate";
import TeamSettings from "../settings/TeamSettings";
import RightSettings from "../settings/RightSettings";
import UserList from "../user/UserList";
import LogoutPopup from "./logoutpopup";
import AccountList from "../account/AccountList";
import AgentsList from "../agents/Agentslist";
import Scripts from "../agents/Scripts";
import ScriptHistory from "../agents/ScriptHistory";
import ScriptHistoryViewer from "../agents/ScriptHistoryViewer";
import Training from "../training/Training";
import userManager from '../auth/UseAuth';
import DomainSettings from "../settings/DomainSettings";
import AlertSettings from "../settings/AlertSettings";
import BillingSettings from "../settings/BillingSettings";
import CodeInstall from "../settings/InstallSettings";
import { useAuth } from "../auth/AuthProvider";
import AuditLogList from "../auditLog/AuditLogList";
import { User } from "../../models/User";
import ShareFeedbackPopup from "../feedback/ShareFeedbackPopup";
import axios from "axios";
import { useTranslation } from "react-i18next";
import { GetAccountsByUser, GetAccountsList } from "../../services/GuideService";
import { AccountContext } from "../account/AccountContext";
import { adminApiService, userApiService, userUrl } from "../../services/APIService";
import { getOrganizationById } from "../../services/OrganizationService";
import { useRtl } from "../../RtlContext";
let accountId: string;
interface Account {
	AccountId: string;
	AccountName: string;
}
//let organizationLanguages : OrganizationLanguage[] = [];
export interface Language {
	LanguageId: string;
	Language: string;
	LanguageCode: string;
	FlagIcon: string;
}

// Complete list of 60 world languages (excluding English as primary language)
// This will be replaced by dynamic data from API
export const availableLanguages: Language[] = [];

// Updated language display map to support more languages
export const languageDisplayMap: { [key: string]: string } = {
	"English": "English",
	"Telugu": "తెలుగు",
	"Hindi": "हिन्दी",
	"Arabic": "العربية",
	"Spanish": "Español",
	"French": "Français",
	"German": "Deutsch",
	"Chinese": "中文",
	"Japanese": "日本語",
	"Korean": "한국어",
	"Portuguese": "Português",
	"Italian": "Italiano",
	"Russian": "русский",
	"Dutch": "Nederlands",
	"Swedish": "Svenska",
	"Norwegian": "Norsk",
	"Danish": "Dansk",
	"Finnish": "Suomi",
	"Polish": "Polski",
	"Turkish": "Türkçe",
	"Greek": "Ελληνικά",
	"Hebrew": "עברית",
	"Thai": "ไทย",
	"Vietnamese": "Tiếng Việt",
	"Indonesian": "Bahasa Indonesia",
	"Malay": "Bahasa Melayu",
	"Filipino": "Filipino",
	"Bengali": "বাংলা",
	"Urdu": "اُردُو",
	"Tamil": "தமிழ்",
	"Gujarati": "ગુજરાતી",
	"Marathi": "मराठी",
	"Kannada": "ಕನ್ನಡ",
	"Malayalam": "മലയാളം",
	"Punjabi": "ਪੰਜਾਬੀ",
	"Odia": "ଓଡ଼ିଆ",
	"Assamese": "অসমীয়া",
	"Persian": "فارسی",
	"Swahili": "Kiswahili",
	"Afrikaans": "Afrikaans",
	"Zulu": "isiZulu",
	"Yoruba": "Yorùbá",
	"Hausa": "Hausa",
	"Amharic": "አማርኛ",
	"Czech": "Čeština",
	"Slovak": "Slovenčina",
	"Hungarian": "Magyar",
	"Romanian": "Română",
	"Bulgarian": "Български",
	"Croatian": "Hrvatski",
	"Serbian": "Српски",
	"Slovenian": "Slovenščina",
	"Lithuanian": "Lietuvių",
	"Latvian": "Latviešu",
	"Estonian": "Eesti",
	"Ukrainian": "Українська",
	"Belarusian": "Беларуская",
	"Macedonian": "Македонски",
	"Albanian": "Shqip",
	"Bosnian": "Bosanski",
	"Montenegrin": "Црногорски"
};


// // Helper to set RTL attribute on body
function setBodyRtlAttribute(rtlValue: boolean) {
	if (rtlValue) {
		document.body.classList.add('rtl');
		document.body.setAttribute('dir', 'rtl');
	}
	else {
		document.body.classList.contains('rtl') && document.body.classList.remove('rtl');
		document.body.removeAttribute('dir');
	}
}

const OrgAdminMenu: React.FC = () => {
	const { t: translate } = useTranslation();
	const [isShareFeedbackPopup, setIsShareFeedbackPopup] = useState(false);
	const [openDropdown, setOpenDropdown] = useState<string | null>(null);

	const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());
	const [openMenu, setOpenMenu] = useState(false);
	const { signOut, userDetails, userRoles } = useAuth();
	const [userType, setUserType] = useState(userDetails?.UserType ?? "");
	const [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId ?? "");
	const [user, setUser] = useState<User | null>(null);
	const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);
	const [openPopup, setOpenPopup] = useState(false);
	const [sidebarLocalOpen, setLocalSidebarOpen] = useState(isSidebarOpen());
	const navigate = useNavigate();
	const [toLanguage, setToLanguage] = useState<string>(() => {
		return userDetails?.Language || localStorage.getItem('selectedLanguage') || 'English';
	});
	const [translatedLabels, setTranslatedLabels] = useState<string[]>([]);
	const location = useLocation();
	const [availableLanguages, setAvailableLanguages] = useState<Language[]>([]);
	const [loading, setLoading] = useState(true);
	const [selectedLanguages, setSelectedLanguages] = useState<Language[]>([]);
	const [languageSearchTerm, setLanguageSearchTerm] = useState('');
	const ORGANIZATION_ID = userDetails?.OrganizationId;
	const [accounts, setAccounts] = useState<Account[]>([]);
	const [selectedAccountId, setSelectedAccountId] = useState('');
	const [accountsFetched, setAccountsFetched] = useState(false);
	const { setAccountId, setRoles } = useContext(AccountContext);
	const firstDropdownRef = useRef<HTMLDivElement | null>(null);
	const secondDropdownRef = useRef<HTMLDivElement | null>(null);
	const { isRtl, setRtl } = useRtl();

	const handleDropdownOpen = (dropdownName: string) => {
		const newState = dropdownName === openDropdown ? null : dropdownName;
		setOpenDropdown(newState);

		if (dropdownName === "second" && newState === null) {
			setLanguageSearchTerm('');
		}
	};


	const handleClickOutside = (event: MouseEvent) => {
		if (
			firstDropdownRef.current && !firstDropdownRef.current.contains(event.target as Node) &&
			secondDropdownRef.current && !secondDropdownRef.current.contains(event.target as Node)
		) {
			setOpenDropdown(null);
			setLanguageSearchTerm(''); // Reset search term when clicking outside
		}
	};
	useEffect(() => {
		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, []);
	useEffect(() => {
		const organizationId = userDetails?.OrganizationId;
		const userType = userDetails?.UserType;

		if (organizationId && userType && !accountsFetched) {
			const fetchAccounts = async () => {
				setLoading(true);
				try {
					if (userType.toLocaleLowerCase() === 'admin') {
						await GetAccountsList(setAccounts, setLoading, organizationId, -1, -1, "", "", "");
					} else if (userType.toLocaleLowerCase() === 'user') {
						await GetAccountsByUser(setAccounts, setLoading, organizationId, -1, -1, "", "", "");
					}
					setAccountsFetched(true);
				} catch (err) {
					console.error(err);
				} finally {
					setLoading(false);
				}
			};

			fetchAccounts();
		}
	}, [userDetails?.OrganizationId, userDetails?.UserType, accountsFetched]);

	const handleAccountChange = (event: any) => {
		const AccountId: string = event.target.value;
		setSelectedAccountId(AccountId);
		setAccountId(AccountId);
		const rolesOfAccount = userType.toLocaleLowerCase() == "admin" ? ["Account Admin"] : userRoles[AccountId];
		setRoles(rolesOfAccount);
		accountId = AccountId;
		localStorage.setItem("CurrentAccountId", accountId);
	};

	useEffect(() => {
		const userInfoString = localStorage.getItem("userInfo");
		if (userInfoString && userInfoString != '{}') {
			try {
				const userInfo = JSON.parse(userInfoString);
				if (userInfo['user']) {
					const parsedUser = JSON.parse(userInfo['user']);
					setUser(parsedUser);
					if (parsedUser) {
						setUserType(parsedUser.UserType?.toLocaleLowerCase() ?? '');
						const OrgId = parsedUser.OrganizationId ?? '';
						setOrganizationId(OrgId);
					}
				}
			} catch (error) {
				console.error("Error parsing userInfo: ", error);
			}
		}
		else if (userDetails) {
			setUser(userDetails);
			if (userDetails) {
				setUserType(userDetails.UserType?.toLocaleLowerCase() ?? '');
				const OrgId = userDetails.OrganizationId ?? '';
				setOrganizationId(OrgId);
			}
		}
	}, [userDetails]);
	// Fetch saved languages from API (excluding English as it's the primary language)
	useEffect(() => {
		const fetchSavedLanguages = async () => {
			try {
				const response = await getLanguages();

				// Set all available languages
				setAvailableLanguages(response);

				// Filter out English since it's the primary language for selected languages
				const filteredLanguages = response.filter((lang: Language) =>
					lang.Language.toLowerCase() !== 'english'
				);
				setSelectedLanguages(filteredLanguages);

				// Only set language from userDetails on initial load, not on every change
				const currentLanguageExists = filteredLanguages.some(lang =>
					lang.Language === toLanguage
				) || toLanguage === 'English';

				// If current language doesn't exist in available languages, fall back to English
				if (!currentLanguageExists) {
					const fallbackLanguage = 'English';
					setToLanguage(fallbackLanguage);
					localStorage.setItem('selectedLanguage', fallbackLanguage);
				}
			} catch (err) {
				console.error("Error fetching saved languages:", err);
				setSelectedLanguages([]);
				setAvailableLanguages([]);
				// Reset to userDetails.Language or English if there's an error
				const fallbackLanguage = userDetails?.Language || 'English';
				setToLanguage(fallbackLanguage);
				localStorage.setItem('selectedLanguage', fallbackLanguage);
			}
		};

		fetchSavedLanguages();
	}, []); // Remove toLanguage and userDetails?.Language dependencies to prevent override
	const ShareFeedbackClick = () => {
		setIsShareFeedbackPopup(true);

	};

	const closeShareFeedback = () => {
		setIsShareFeedbackPopup(false);

	};
	useEffect(() => {
		const storedSelectedLanguages = localStorage.getItem("selectedLanguages");
		if (storedSelectedLanguages) {
			// This can be removed as we now fetch from API
			// setSelectedLanguages(JSON.parse(storedSelectedLanguages));
		}
	}, []);


	const getLanguageName = (languageCode: string): string => {
		const language = availableLanguages.find((lang) => lang.LanguageCode === languageCode);
		return language ? language.Language : "Unknown";
	};

	const handleToLanguageChange = async (event: any) => {
		const selectedLanguage = event.target.value;
		console.log('Language change attempted:', selectedLanguage);

		// Immediately update the state
		setToLanguage(selectedLanguage);

		// Find the language code from availableLanguages
		const languageObj = availableLanguages.find(lang => lang.Language === selectedLanguage);
		const languageCode = languageObj ? languageObj.LanguageCode : 'en';

		localStorage.setItem('selectedLanguage', selectedLanguage);
		localStorage.setItem('selectedLanguageCode', languageCode);

		// Call updateLanguage API with the language name
		try {
			await updateLanguage(selectedLanguage);
			console.log('Language updated successfully:', selectedLanguage);
		} catch (error) {
			console.error('Error updating language:', error);
		}
	};

	const [labelsNew, setLabelsNew] = useState<any>({});
	useEffect(() => {
		const fetchLabelsNew = async (toLanguage: any) => {
			if (toLanguage === "English") {
				i18n.changeLanguage('en');
				return;
			}

			try {
				setLoading(true);
				const data = await getLabels(toLanguage, false);
				if (data) {
					// Find the language code from selectedLanguages or availableLanguages
					const languageObj = selectedLanguages.find(lang => lang.Language === toLanguage) ||
						availableLanguages.find(lang => lang.Language === toLanguage);
					const languageKey = languageObj ? languageObj.LanguageCode : '';

					if (languageKey) {
						let parsedLabelsNew: any = {};

						// Handle new array format
						if (Array.isArray(data)) {
							data.forEach((item: any) => {
								if (item.LabelName && item[toLanguage] !== undefined) {
									parsedLabelsNew[item.LabelName] = item[toLanguage];
								}
							});
						} else if (data[languageKey]) {
							// Fallback for old object format
							parsedLabelsNew = data[languageKey];
						}

						if (Object.keys(parsedLabelsNew).length > 0) {
							setLabelsNew(parsedLabelsNew);
							i18n.addResourceBundle(languageKey, 'translation', parsedLabelsNew, true, true);
							i18n.changeLanguage(languageKey);
						}
					}
				}
			} catch (err) {
				console.error('Error fetching labels:', err);
			} finally {
				setLoading(false);
			}
		};

		fetchLabelsNew(toLanguage);
	}, [toLanguage, selectedLanguages, availableLanguages]);


	const handleMenuClick = () => {
		setOpenMenu((prev) => !prev);
	};

	useEffect(() => {
		const unsubscribe = subscribe(setLocalSidebarOpen);
		return () => unsubscribe();
	}, []);

	const handleHomeClick = () => {
		startTransition(() => {
			navigate("/");
		});
	};

	const handleTogglePopover = (event: any) => {
		if (anchorEl) {
			// Close the popover if it's open
			setAnchorEl(null);
		} else {
			// Open the popover and set the anchor element
			setAnchorEl(event.currentTarget);
		}
	};

	const handleOrgClick = () => {
		startTransition(() => {
			navigate("/organizations");
		});
	};

	const handleAccountClick = () => {
		startTransition(() => {
			navigate("/accounts");
		});
	};

	const handleGuideClick = () => {
		startTransition(() => {
			navigate("/guide");
		});
	};

	const handleAccSeetingsClick = () => {
		startTransition(() => {
			navigate("/settings/account");
		});
	};

	const handleTeamClick = () => {
		startTransition(() => {
			navigate("/settings/team");
		});
	};

	const handleThemesClick = () => {
		startTransition(() => {
			navigate("/theme");
		});
	};

	const handleBillingClick = () => {
		startTransition(() => {
			navigate("/settings/billing");
		});
	};

	const handleInstallClick = () => {
		startTransition(() => {
			navigate("/settings/install");
		});
	};

	const handlenotifyClick = () => {
		startTransition(() => {
			navigate("/notifications");
		});
	};

	const handleProfileClick = () => {

		navigate("/viewprofile");

		setAnchorEl(null);
	};

	const handleClick = (path: string) => {
		switch (path) {
			case "user":
				return navigate("/user");
			case "account":
				return navigate("/settings/account");
			case "settings":
				return navigate("/settings");
			case "auditlogs":
				return navigate("/superadmin/auditlogs");
			case "organization":
				return navigate("/superadmin/organizations");
			case "multilingual":
				return navigate("/superadmin/Multilingual");
			default:
				return navigate("/");
		}
	};



	const handleQuickAdoptClick = () => {
		const logedUsertype = localStorage.getItem('userType');
		if (logedUsertype !== "SuperAdmin") {
			startTransition(() => {
				navigate("/");
			});
		}
	};
	useEffect(() => {
		if (accounts.length > 0) {
			setSelectedAccountId(accounts[0].AccountId);
			setAccountId(accounts[0].AccountId)
			accountId = accounts[0].AccountId
			localStorage.setItem("CurrentAccountId", accountId);
			const rolesOfAccount = userType.toLocaleLowerCase() == "admin" ? ["Account Admin"] : userRoles[accounts[0].AccountId];
			setRoles(rolesOfAccount);

		}
	}, [accounts]);
	const handleLogoutClick = () => {
		setOpenPopup(true);
		setAnchorEl(null);
	};
	const toggleSidebar = () => {
		setSidebarOpen(!sidebarLocalOpen);
	};
	// Fetch organization and account RTL and set body attribute
	useEffect(() => {
		async function fetchAndSetRtl() {
			if (OrganizationId) {
				try {
					// Fetch organization RTL
					const org = await getOrganizationById(OrganizationId);
					let orgRtl = org && typeof org.RTL !== 'undefined' ? org.RTL : false;

					// Fetch accounts and get first account's Rtl if available
					let accountRtl = false;
					const requestBody = {
						skip: -1,
						top: -1,
						filters: '',
						orderByFields: '',
					};
					const response = await adminApiService.post(`/Account/GetAccountsByOrgId`, requestBody);
					const accounts = response.data.results;
					if (Array.isArray(accounts) && accounts.length > 0) {
						const selectedAccount = accounts.find((acc: any) => acc.AccountId === selectedAccountId);
						if (selectedAccount && typeof selectedAccount.Rtl !== 'undefined') {
							accountRtl = selectedAccount.Rtl;
						}
					}
					setRtl(accountRtl); // Use context setter
				} catch (e) {
					console.error('Failed to fetch account for RTL:', e);
				}

			}
		}
		fetchAndSetRtl();
	}, [OrganizationId, selectedAccountId, setRtl]);
	useEffect(() => {
		setBodyRtlAttribute(isRtl);
	}, [isRtl]);
	return (
		<div>
			<div className="qadpt-banner">
				<div>
					<div className="adapat-banner-left" >
						<div>
							<img
								src={logo}
								alt="QuickAdopt Logo"
								className="qadpt-logo"
							/>
						</div>
					</div>

				</div>
				<div className="adapat-banner-right">
					{openMenu && userType.toLocaleLowerCase() === "superadmin" ? (
						<div className="qadpt-settings-container">
							<a
								onClick={() => handleClick("organization")}
								className="qadapt-link"
							>
								{translate('Organization')}
							</a>
							<a
								onClick={() => handleClick("auditlogs")}
								className="qadapt-link"
							>
								{translate('Auditlogs')}
							</a>

							<a
								onClick={() => handleClick("multilingual")}
								className="qadapt-link"
							>
								{translate('Multilingual')}
							</a>
						</div>
					) : null}
					{userType.toLocaleLowerCase() === "superadmin" && (
						<i className="fal fa-admenu menu-icon"
							data-testid="cog-tci"
							onClick={handleMenuClick}>
						</i>
					)}
					{/* <i className="fal fa-bell menu-icon"></i> */}
					{userType !== "superadmin" ? (

						<div className="qxy-lang" style={{ width: "auto" }} >
							<FormControl variant="standard" className="lang-input">
								<Select
									value={selectedAccountId}
									onChange={handleAccountChange}

									displayEmpty
									fullWidth
									startAdornment={
										<InputAdornment position="start">
											<img
												src={profile}
												alt="profile Logo"

											/>
										</InputAdornment>
									}
									endAdornment={
										<InputAdornment position="end">
											<img
												src={ArrowDown}
												alt="Arrowdown Logo"
												style={{
													cursor: 'pointer', zoom: "0.9", transform: openDropdown === "first" ? 'rotate(180deg)' : 'rotate(0deg)',
													transition: 'transform 0.3s ease'
												}}
												onClick={(e) => {
													e.stopPropagation(); // Prevent default select behavior if needed
													handleDropdownOpen("first"); // Trigger dropdown open
												}}
											/>
										</InputAdornment>
									}
									open={openDropdown === "first"}
									onClose={() => setOpenDropdown(null)}
									onOpen={() => handleDropdownOpen("first")}
									IconComponent={() => null}
									sx={{
										"&:before, &:after": {
											display: "none",
										},
									}}
									MenuProps={{
										PaperProps: {
											sx: {
												width: '200px', // Dropdown width
												maxHeight: '305px', // Maximum height for dropdown
												overflowY: 'auto', // Enable vertical scrolling
												marginTop: '5px', // Small gap from select input
												'& .MuiMenuItem-root': {
													fontSize: '14px', // Font size for dropdown items
													whiteSpace: 'normal', // Text wrapping
													wordBreak: 'break-word', // Word breaking
												},
											},
										},
									}}
								>
									{accounts.length > 0 ? (
										accounts.map((account) => (
											<MenuItem key={account.AccountId} value={account.AccountId} >
												{account.AccountName}
											</MenuItem>
										))
									) : (
										<MenuItem disabled>No accounts available</MenuItem>
									)}
								</Select>
							</FormControl>
						</div>
					) : null}


					{location.pathname !== "settings/multilingual" && userType !== "superadmin" ? (
						<div className="qxy-lang">
							<Box>
								<FormControl variant="standard" className="lang-input">
									<InputLabel id="demo-simple-select"></InputLabel>
									<Select
										className="lang-input-box"
										labelId="demo-simple-select"
										id="demo-simple"
										value={toLanguage} // Controlled component
										onChange={handleToLanguageChange}
										startAdornment={
											<InputAdornment position="start">
												<img src={global} alt="global Logo" />
											</InputAdornment>
										}
										endAdornment={
											<InputAdornment position="end">
												<img
													src={ArrowDown}
													alt="Arrowdown Logo"
													style={{
														cursor: 'pointer',
														zoom: '0.9',
														transform: openDropdown === "second" ? 'rotate(180deg)' : 'rotate(0deg)',
														transition: 'transform 0.3s ease',
													}}
													onClick={(e) => {
														e.stopPropagation(); // Prevent default select behavior
														setOpenDropdown(openDropdown === "second" ? null : "second"); // Toggle logic
													}}
												/>
											</InputAdornment>
										}
										IconComponent={() => null}
										open={openDropdown === "second"}
										onClose={() => {
											setOpenDropdown(null);
											setLanguageSearchTerm('');
										}}
										onOpen={() => handleDropdownOpen("second")}
										sx={{
											"&:before, &:after": {
												display: "none",
											},
										}}
										MenuProps={{
											PaperProps: {
												sx: {
													width: '250px',
													maxHeight: '350px',
													overflowY: 'auto',
													marginTop: '5px',
													'& .MuiMenuItem-root': {
														fontSize: '14px',
														whiteSpace: 'normal',
														wordBreak: 'break-word',
													},
													'& .search-container': {
														position: 'sticky',
														top: 0,
														backgroundColor: 'white',
														borderBottom: '1px solid #e0e0e0',
														padding: '8px 16px',
														zIndex: 1,
													},
													'& .search-input': {
														width: '100%',
														fontSize: '14px',
														padding: '8px 12px',
														border: '1px solid #ccc',
														borderRadius: '4px',
														outline: 'none',
														'&:focus': {
															borderColor: '#1976d2',
														},
													},
												},
											},
										}}
									>
										<div className="search-container">
											<input
												type="text"
												className="search-input"
												placeholder="Search languages..."
												value={languageSearchTerm}
												onChange={(e) => {
													e.stopPropagation();
													setLanguageSearchTerm(e.target.value);
												}}
												onKeyDown={(e) => e.stopPropagation()}
												onClick={(e) => e.stopPropagation()}
											/>
										</div>
										<MenuItem key="en" value="English">
											English
										</MenuItem>
										{selectedLanguages
											.filter((lang) =>
												lang.Language.toLowerCase().includes(languageSearchTerm.toLowerCase())
											)
											.sort((a, b) => a.Language.localeCompare(b.Language))
											.map((lang) => (
												<MenuItem key={lang.LanguageId} value={lang.Language} sx={{ fontSize: '14px' }}>
													{languageDisplayMap[lang.Language] || lang.Language}
												</MenuItem>
											))}
									</Select>
								</FormControl>


							</Box>

						</div>
					) : null}
					<div
						className="qadpt-prof"
						onClick={handleTogglePopover}
					>
						<span style={{ display: "flex", marginRight: "0.5rem !important" }}>
							<span>
								<div className="prof-div">
									<span className="prof-span">{initialssData ? initialssData : initialsData}</span>
								</div>
							</span>
						</span>
					</div>
					<div className="profilepopup">
						<Popover
							open={Boolean(anchorEl)}
							anchorEl={anchorEl}
							//onClose={() => setAnchorEl(null)}
							onClose={handleTogglePopover}
							className="qadpt-prfpopup"
							anchorOrigin={{
								vertical: "top",
								horizontal: "center",
							}}
							transformOrigin={{
								vertical: "bottom",
								horizontal: "center",
							}}
							PaperProps={{
								style: {
									marginTop: '32px',
								},
							}}
							sx={{
								"& .MuiPaper-root": {
									width: "195px",
									height: "auto",
									borderRadius: "7px",
									padding: "10px 12px",
									backgroundColor: "#ffffff",
									boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
									zIndex: 9999,
								},
							}}
						>
							<Box
								display="flex"
								flexDirection="column"
								alignItems="center"
							>
								<Box
									width="100%"
								>
									<Button
										fullWidth
										onClick={ShareFeedbackClick}
										sx={{

											padding: '10px 0',
											gap: '10px',
											justifyContent: 'flex-start',
											textTransform: 'none',
										}}
									>
										<img
											src={Feedback}
											alt="Feedback Icon"
											style={{ width: '18px', height: '18px', marginRight: '8px' }}
										/>
										<Typography
											sx={{
												fontFamily: 'Poppins',
												fontSize: '14px',
												fontWeight: 400,
												lineHeight: '21px',
												letterSpacing: '0.3px',
												textAlign: 'left',
												color: '#202224',
											}}
										>
											{translate('Share Feedback')}
										</Typography>
									</Button>
									<Button
										fullWidth
										onClick={handleProfileClick}
										sx={{

											padding: '10px 0',
											gap: '10px',
											justifyContent: 'flex-start',
											textTransform: 'none',
										}}
									>
										<AccountCircleOutlinedIcon style={{ color: 'black' }} />
										<Typography
											sx={{
												fontFamily: 'Poppins',
												fontSize: '14px',
												fontWeight: 400,
												lineHeight: '21px',
												letterSpacing: '0.3px',
												textAlign: 'left',
												color: '#202224',
											}}
										>
											{translate('View Profile')}
										</Typography>
									</Button>
									<Button
										fullWidth
										onClick={handleLogoutClick}
										sx={{

											padding: '10px 0',
											gap: '10px',
											justifyContent: 'flex-start',
											textTransform: 'none',
										}}
										className="qadpt-logout"
									>
										<img
											src={Logout}
											alt="Logout Icon"
											style={{ width: '18px', height: '18px', marginRight: '8px' }}
										/>
										<Typography
											sx={{
												fontFamily: 'Poppins',
												fontSize: '14px',
												fontWeight: 400,
												lineHeight: '21px',
												letterSpacing: '0.3px',
												textAlign: 'left',
												color: '#202224',
											}}
										>
											{translate('Logout')}
										</Typography>
									</Button>
								</Box>
							</Box>
						</Popover>
					</div>
				</div>
			</div>
			<ShareFeedbackPopup
				open={isShareFeedbackPopup}
				onClose={closeShareFeedback}
				setIsShareFeedbackPopup={setIsShareFeedbackPopup}
			/>
			<div className="qadpt-page-content">
				{userType.toLocaleLowerCase() !== "superadmin" ? <SideMenu selectedLanguageProp={toLanguage} /> : null}
				{openPopup ? (
					<div className="qadpt-modal-overlay">
						<div className="qadpt-usrconfirm-popup qadpt-success">
							<div>
								<div className="qadpt-icon">
									<i className="fal fa-sign-out-alt" /> {/* Or any logout icon */}
								</div>
							</div>
							<div className="qadpt-popup-title">{translate('Confirmation')}</div>
							<div className="qadpt-warning">
								{translate('Are you sure you want to logout?')}
							</div>
							<div className="qadpt-buttons">
								<button
									onClick={() => setOpenPopup(false)}
									className="qadpt-cancel-button"
								>
									{translate('Cancel')}
								</button>
								<button
									onClick={async () => {
										setOpenPopup(false);
										if (userDetails?.UserType !== "SuperAdmin") {
											try {
												const response = await userApiService.get(`/User/Logout`);
												if (response.status === 401) {
													localStorage.clear();
													sessionStorage.clear();
													navigate("/login");
													window.location.reload();
												}
												signOut();
												window.location.reload();
											} catch (error) {
												// Optional: handle error here
											}
										} else {
											localStorage.clear();
											sessionStorage.clear();
											document.cookie.split(";").forEach((cookie) => {
												const [name] = cookie.split("=");
												document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
											});
											navigate("/admin/adminlogin");
											window.location.reload();
										}
									}}
									className="qadpt-conform-button"
									type="button"
								>
									{translate('Logout')}
								</button>
							</div>
						</div>
					</div>
				) : null}

				<div
					className={`qadpt-settings-content ${(
							[
								`/${userDetails?.OrganizationId}/accounts`,
								`/${userDetails?.OrganizationId}/roles`,
								`/${userDetails?.OrganizationId}/team`,
								"/settings/domains",
								"/settings/rights",
								"/settings/alerts",
								"/settings/billing",
								"/settings/install",
								"/settings/activitylog",
								"/settings/multilingual",
								"/settings/agents",
								"/settings/scripts",
								"/settings/scripthistory",
								"/settings/scripthistoryviewer",
								"/settings/training",
								"/viewprofile"
							].includes(location.pathname)
						) ? '' : 'qadpt-hide-smenu'
						}
   ${location.pathname === "/viewprofile" ? 'qadpt-viewprofile-page' : ''
						}`}
				>
					{location.pathname === `/${userDetails?.OrganizationId}/accounts` && userType === "admin" ? (
						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<AccountList />
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>

					) : null}


					{location.pathname === "/viewprofile" ? (
						<PageWrapper>
							<div style={{ display: 'flex', flexDirection: 'row', width: '100%' }}>
								<div>
									<ProfileSettings selectedLanguageProp={getLanguageName(toLanguage)} />
								</div>
							</div>
						</PageWrapper>
					) : null}

					{location.pathname === `/settings/multilingual` ? (
						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<LanguageProvider>
												<Translater />
											</LanguageProvider>
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>

					) : null}

					{location.pathname === `/${userDetails?.OrganizationId}/team` && userType === "admin" ? (
						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<UserList />
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>

					) : null}

					{location.pathname === `/${userDetails?.OrganizationId}/roles` && userType === "admin" ? (
						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<TeamSettings />
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>

					) : null}

					{location.pathname === "/settings/domains" && userType === "admin" ? (

						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<DomainSettings />
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>


					) : null}
					{location.pathname === "/settings/rights" ? (

						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<RightSettings />
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>
					) : null}

					{location.pathname === "/settings/alerts" ? (
						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<AlertSettings />
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>
					) : null}
					{location.pathname === "/settings/billing" && userType === "admin" ? (
						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<BillingSettings />
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>
					) : null}
					{location.pathname === "/settings/install" ? (
						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<CodeInstall />
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>

					) : null}
					{location.pathname === "/settings/activitylog" ? (
						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<AuditLogList />
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>

					) : null}
					{location.pathname === `/settings/agents` ? (
						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<AgentsList />
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>

					) : null}

					{location.pathname === `/settings/scripts` ? (
						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<Scripts />
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>

					) : null}

					{location.pathname === `/settings/scripthistory` ? (
						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<ScriptHistory />
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>

					) : null}

					{location.pathname === `/settings/scripthistoryviewer` ? (
						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<ScriptHistoryViewer />
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>

					) : null}

					{location.pathname === `/settings/training` ? (
						<div className="qadpt-settings-page">
							<Box>
								<PageWrapper>
									<div className="qadpt-settings-smenu">
										<Box id="settingMenuBox">
											<Settings />
										</Box>
									</div>
									<div className="usr-list-page">
										<Box>
											<Training />
										</Box>
									</div>

								</PageWrapper>
							</Box>
						</div>

					) : null}


				</div>
			</div>
		</div>
	);
};

export default OrgAdminMenu;

export { accountId }