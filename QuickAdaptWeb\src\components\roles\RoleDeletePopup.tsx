import { Button } from "@mui/material";
import { deleteUserRole } from "../../services/UserRoleService";
import { useTranslation } from "react-i18next";

const ConfirmationDialogDelete = (props:any) => {
    const {
        fetchUserRoles,
        roleToDelete,
        setRoleToDelete,
        confirmDialogOpen,
        setConfirmDialogOpen,
        userIdroletodelete,
        openSnackbar
    } = props;
    const handleDeleteRole = async () => {
        if (roleToDelete) {
            try {
                await deleteUserRole(roleToDelete, userIdroletodelete, openSnackbar);
                fetchUserRoles();
            } catch (error) {
                console.error('Failed to delete role', error);
            }
        }
        setConfirmDialogOpen(false);
        setRoleToDelete(null);
    };
    const { t: translate } = useTranslation();
    return (

        confirmDialogOpen && (
            <div className="qadpt-modal-overlay"> 
            <div className="qadpt-usrconfirm-popup qadpt-success qadpt-rolepopup">
              {/* <div>
                <div className="qadpt-icon">
                  <CorporateFareIcon />
                </div> 
              </div> */}
              <div className="qadpt-popup-title">
                {translate('Confirm Deletion')}
              </div>
              <div className="qadpt-warning">
                {translate('Are you sure you want to delete this role?')}
              </div>
              <div className="qadpt-buttons">
                <button
                  onClick={() => setConfirmDialogOpen(false)}
                  className="qadpt-cancel-button"
                >
                  {translate('Cancel')}
                </button>
                <button
                  onClick={handleDeleteRole}
                  className="qadpt-conform-button"
                  type="button"
                >
                  {translate('Yes, Delete')}
                </button>
              </div>
            </div>
          </div>
        
        )
    );
}
export default ConfirmationDialogDelete
