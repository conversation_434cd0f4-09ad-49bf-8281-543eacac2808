import React, { useState, ChangeEvent } from "react";
import { TextField, Button, Snackbar, Alert, Grid, Typography,FormControl } from "@mui/material";
import { useSnackbar } from "../../SnackbarContext";
import { useAuth } from "../auth/AuthProvider";
import { saveGuide,CheckGuideNameExists } from "../../services/GuideService";
import { accountId } from "../adminMenu/AdminMenu";
import { useTranslation } from "react-i18next";
import { openGuideInBuilder } from '../../utils/openGuideInBuilder';
interface CreateNewPopupProps {
    setTourslist: (tours: any) => void;
    setLoading?: (loading: boolean) => void; 
    setShowPopup: (show: boolean) => void;
    showPopup: boolean;
    setTotalcount: (count: number) => void;
    setActiveTab: (tab: number) => void;
    activeTab: number;
    changeTab: () => void;
    name: string;
}

const CreateNewPopup: React.FC<CreateNewPopupProps> = (props) => {
    const {
        setTourslist,
        setLoading,
        setShowPopup,
        showPopup,
        setTotalcount,
        setActiveTab,
        changeTab,
        name
    } = props;

    const { openSnackbar } = useSnackbar();
    const { userDetails } = useAuth();
    const [inputs, setInputs] = useState({
        GuideName: "",
        TargetUrl: "",
    });
    const [errors, setErrors] = useState<Partial<Record<string, string>>>({});
    const [isValid, setIsValid] = useState(false);

    const [snackbarOpen, setSnackbarOpen] = useState(false);
    const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">("success");
  const { t: translate } = useTranslation();
    const [guideNameTimer, setGuideNameTimer] = useState<NodeJS.Timeout | null>(null);

    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
      const { name, value } = event.target;
      const updatedInputs = { ...inputs, [name]: value };
      let error = ""; 
      if (name === "GuideName") {
        if (guideNameTimer) clearTimeout(guideNameTimer);
        if (value.length > 50) {
          error = translate("Guide Name should not exceed 50 characters.");
          setErrors((prev) => ({ ...prev, [name]: error }));
        } else {
          setErrors((prev) => ({ ...prev, [name]: "" }));
          const timer = setTimeout(() => {
            if (value.trim().length < 3) {
              setErrors((prev) => ({ ...prev, [name]: translate("Guide Name must be at least 3 characters.")}));
            }
          }, 1000);
          setGuideNameTimer(timer);
        }
      }
      if (name === "TargetUrl") {
          const urlPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/.*)?$/;
          if (value.length > 200) {
              error = translate("Target URL must be less than 200 characters.");
          } else if (/[^a-zA-Z0-9-._~:/?#[\]@!$&'()*+,;=]/.test(value)) {
              error = translate("Target URL contains invalid characters.");
          } else if (!urlPattern.test(value)) {
              error = `${translate("Please enter a valid URL. E.g.")}, https://example.com"`;
          }
      }
      setInputs(updatedInputs);
      setErrors((prev) => ({ ...prev, [name]: error }));
  
      const guideNameValid =
          updatedInputs.GuideName.trim().length >= 3 &&
          updatedInputs.GuideName.trim().length <= 50 &&
          !errors.GuideName &&
          (name !== "GuideName" || !error); // ensure current field error is not skipped
  
      const urlValid =
          updatedInputs.TargetUrl.trim().length > 0 &&
          updatedInputs.TargetUrl.length <= 200 &&
          !errors.TargetUrl &&
          (name !== "TargetUrl" || !error);
  
      setIsValid(guideNameValid && urlValid);
  };

    const handleSubmitTour = async (event: any) => {
        event.preventDefault();
        const newErrors: Partial<Record<string, string>> = {};
        let isValid = true;

        if (!inputs.GuideName) {
            newErrors.GuideName = translate("Guide Name is required.");
            isValid = false;
        } else if (inputs.GuideName.length < 3) {
            newErrors.GuideName = translate("Guide Name must be at least 3 characters.");
            isValid = false;
        }

        if (!inputs.TargetUrl) {
            newErrors.TargetUrl = "Target URL is required.";
            isValid = false;
        } else if (!/^https?:\/\/\S+\.\S+$/.test(inputs.TargetUrl)) {
            newErrors.TargetUrl = "Enter a valid URL.";
            isValid = false;
        }

        if (isValid) {
            const newGuide = {
                GuideId: "",
                GuideType: name,
                Name: inputs.GuideName,
                Content: "",
                OrganizationId: userDetails?.OrganizationId,
                CreatedDate: new Date().toISOString(),
                CreatedBy: userDetails?.UserName,
                UpdatedBy: userDetails?.UserName,
                TargetUrl: inputs.TargetUrl,
                Frequency: "One-time",
                Segment: "All users",
                AccountId: accountId,
                GuideStatus: "Draft",
                GuideStep: [],
            };

            try {
                if (newGuide.OrganizationId) {
                    
                    const checkGuideNamevalidation = await CheckGuideNameExists(newGuide.Name, newGuide.AccountId,name);
                    if (checkGuideNamevalidation) {
                        newErrors.GuideName = translate("Guide Name Already Exists");
                        setErrors(newErrors);
                    }
                    const response = await saveGuide(newGuide);
                    if (response.Success) {
                        openSnackbar(`${inputs.GuideName} ${name}  ${translate("created successfully. Please Check The Drafts")}`, "success");
                        let url = newGuide.TargetUrl;
                        const guideId = response.Data;
                        if (guideId) {
                            openGuideInBuilder(newGuide.TargetUrl, guideId, newGuide.AccountId);
                        }
                        changeTab(); 
                        setActiveTab(2)
                        setShowPopup(false);
                    } else {
                        openSnackbar(translate(response.ErrorMessage) || translate("Failed to create guide"), "error");
                    }
                }
            } catch (error) {
                openSnackbar(`${translate("An unexpected error occurred")}: ${(error instanceof Error ? error.message : String(error))}`, "error");
            } finally {
                setSnackbarOpen(true);
            }
        } else {
            setErrors(newErrors);
        }
    };

    const handleSnackbarClose = () => {
        setSnackbarOpen(false);
    };

    React.useEffect(() => {
      return () => {
        if (guideNameTimer) clearTimeout(guideNameTimer);
      };
    }, [guideNameTimer]);

    return (
        <div className="qadpt-createpopup">
            {showPopup && (
               <div>
               <div className="qadpt-title-sec">
              <div className="qadpt-title">{`${translate("Create")} ${translate(name)}`}</div>
                 <div className="qadpt-sub-title">
                {`${translate("Type in the")} ${translate(name)} ${translate("name and the URL of the webpage, where you want to create your")} ${translate(name)}`}
                 </div>
                 <svg
                   onClick={() => setShowPopup(false)}
                   className="qadpt-closeicon"
                   xmlns="http://www.w3.org/2000/svg"
                   width="24"
                   height="24"
                   viewBox="0 0 50 50"
                 >
                   <path d="M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z" />
                 </svg>
               </div>
               <div className="qadpt-createflds">
                 <Grid container spacing={2}>
                   <Grid item xs={12}>
                     <FormControl fullWidth required>
                    <label htmlFor="guide-name">{translate(name)} {translate("Name")}</label>
                       <TextField
                         name="GuideName"
                         required
                         value={inputs.GuideName}
                         onChange={handleChange}
                      placeholder={name && `${translate("Enter")} ${translate(name)} ${translate("Name")}`}
                         helperText={errors.GuideName}
                         variant="outlined"
                         error={!!errors.GuideName}
                         inputProps={{ maxLength: 50 }}
                         className="qadpt-createfield"
                       />
                     </FormControl>
                   </Grid>
                   <Grid item xs={12}>
                     <FormControl fullWidth required>
                    <label htmlFor="target-url">{translate("Target URL")}</label>
                       <TextField
                         id="target-url"
                         name="TargetUrl"
                         required
                         value={inputs.TargetUrl}
                         onChange={handleChange}
                         placeholder="E.g., https://example.com"
                         helperText={errors.TargetUrl}
                         variant="outlined"
                         error={!!errors.TargetUrl}
                         inputProps={{ maxLength: 200 }}
                         className="qadpt-createfield"
                       />
                     </FormControl>
                   </Grid>
                 </Grid>
               </div>
               <div className="qadpt-save-btn">
                 <Button
                   type="submit"
                   variant="contained"
                   onClick={handleSubmitTour}
                   color="primary"
                   disabled={!isValid}
                    sx={{
                      opacity: !isValid ? 0.5 : 1,
                    }}
                 >
                {translate("Create & Open in Builder")}
                 </Button>
               </div>
             </div>
             
            )}
        </div>
    );
};

export default CreateNewPopup;
