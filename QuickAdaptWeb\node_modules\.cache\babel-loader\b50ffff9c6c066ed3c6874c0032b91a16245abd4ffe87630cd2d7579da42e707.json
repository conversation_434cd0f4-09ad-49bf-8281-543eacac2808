{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\common\\\\CreateNewGuidePopup.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { TextField, Button, Grid, FormControl } from \"@mui/material\";\nimport { useSnackbar } from \"../../SnackbarContext\";\nimport { useAuth } from \"../auth/AuthProvider\";\nimport { saveGuide, CheckGuideNameExists } from \"../../services/GuideService\";\nimport { accountId } from \"../adminMenu/AdminMenu\";\nimport { useTranslation } from \"react-i18next\";\nimport { openGuideInBuilder } from '../../utils/openGuideInBuilder';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreateNewPopup = props => {\n  _s();\n  const {\n    setTourslist,\n    setLoading,\n    setShowPopup,\n    showPopup,\n    setTotalcount,\n    setActiveTab,\n    changeTab,\n    name\n  } = props;\n  const {\n    openSnackbar\n  } = useSnackbar();\n  const {\n    userDetails\n  } = useAuth();\n  const [inputs, setInputs] = useState({\n    GuideName: \"\",\n    TargetUrl: \"\"\n  });\n  const [errors, setErrors] = useState({});\n  const [isValid, setIsValid] = useState(false);\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarSeverity, setSnackbarSeverity] = useState(\"success\");\n  const {\n    t: translate\n  } = useTranslation();\n  const [guideNameTimer, setGuideNameTimer] = useState(null);\n  const handleChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n    const updatedInputs = {\n      ...inputs,\n      [name]: value\n    };\n    let error = \"\";\n    if (name === \"GuideName\") {\n      if (guideNameTimer) clearTimeout(guideNameTimer);\n      if (value.length > 50) {\n        error = translate(\"Guide Name should not exceed 50 characters.\");\n        setErrors(prev => ({\n          ...prev,\n          [name]: error\n        }));\n      } else {\n        setErrors(prev => ({\n          ...prev,\n          [name]: \"\"\n        }));\n        const timer = setTimeout(() => {\n          if (value.trim().length < 3) {\n            setErrors(prev => ({\n              ...prev,\n              [name]: translate(\"Guide Name must be at least 3 characters.\")\n            }));\n          }\n        }, 1000);\n        setGuideNameTimer(timer);\n      }\n    }\n    if (name === \"TargetUrl\") {\n      const urlPattern = /^(https?:\\/\\/)?([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}(\\/.*)?$/;\n      if (value.length > 200) {\n        error = translate(\"Target URL must be less than 200 characters.\");\n      } else if (/[^a-zA-Z0-9-._~:/?#[\\]@!$&'()*+,;=]/.test(value)) {\n        error = translate(\"Target URL contains invalid characters.\");\n      } else if (!urlPattern.test(value)) {\n        error = `${translate(\"Please enter a valid URL. E.g.\")}, https://example.com\"`;\n      }\n    }\n    setInputs(updatedInputs);\n    setErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n    const guideNameValid = updatedInputs.GuideName.trim().length >= 3 && updatedInputs.GuideName.trim().length <= 50 && !errors.GuideName && (name !== \"GuideName\" || !error); // ensure current field error is not skipped\n\n    const urlValid = updatedInputs.TargetUrl.trim().length > 0 && updatedInputs.TargetUrl.length <= 200 && !errors.TargetUrl && (name !== \"TargetUrl\" || !error);\n    setIsValid(guideNameValid && urlValid);\n  };\n  const handleSubmitTour = async event => {\n    event.preventDefault();\n    const newErrors = {};\n    let isValid = true;\n    if (!inputs.GuideName) {\n      newErrors.GuideName = translate(\"Guide Name is required.\");\n      isValid = false;\n    } else if (inputs.GuideName.length < 3) {\n      newErrors.GuideName = translate(\"Guide Name must be at least 3 characters.\");\n      isValid = false;\n    }\n    if (!inputs.TargetUrl) {\n      newErrors.TargetUrl = \"Target URL is required.\";\n      isValid = false;\n    } else if (!/^https?:\\/\\/\\S+\\.\\S+$/.test(inputs.TargetUrl)) {\n      newErrors.TargetUrl = \"Enter a valid URL.\";\n      isValid = false;\n    }\n    if (isValid) {\n      const newGuide = {\n        GuideId: \"\",\n        GuideType: name,\n        Name: inputs.GuideName,\n        Content: \"\",\n        OrganizationId: userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId,\n        CreatedDate: new Date().toISOString(),\n        CreatedBy: userDetails === null || userDetails === void 0 ? void 0 : userDetails.UserName,\n        UpdatedBy: userDetails === null || userDetails === void 0 ? void 0 : userDetails.UserName,\n        TargetUrl: inputs.TargetUrl,\n        Frequency: \"One-time\",\n        Segment: \"All users\",\n        AccountId: accountId,\n        GuideStatus: \"Draft\",\n        GuideStep: []\n      };\n      try {\n        if (newGuide.OrganizationId) {\n          const checkGuideNamevalidation = await CheckGuideNameExists(newGuide.Name, newGuide.AccountId, name);\n          if (checkGuideNamevalidation) {\n            newErrors.GuideName = translate(\"Guide Name Already Exists\");\n            setErrors(newErrors);\n          }\n          const response = await saveGuide(newGuide);\n          if (response.Success) {\n            openSnackbar(`${inputs.GuideName} ${name}  ${translate(\"created successfully. Please Check The Drafts\")}`, \"success\");\n            let url = newGuide.TargetUrl;\n            const guideId = response.Data;\n            if (guideId) {\n              openGuideInBuilder(newGuide.TargetUrl, guideId, newGuide.AccountId);\n            }\n            changeTab();\n            setActiveTab(2);\n            setShowPopup(false);\n          } else {\n            openSnackbar(translate(response.ErrorMessage) || translate(\"Failed to create guide\"), \"error\");\n          }\n        }\n      } catch (error) {\n        openSnackbar(`${translate(\"An unexpected error occurred\")}: ${error instanceof Error ? error.message : String(error)}`, \"error\");\n      } finally {\n        setSnackbarOpen(true);\n      }\n    } else {\n      setErrors(newErrors);\n    }\n  };\n  const handleSnackbarClose = () => {\n    setSnackbarOpen(false);\n  };\n  React.useEffect(() => {\n    return () => {\n      if (guideNameTimer) clearTimeout(guideNameTimer);\n    };\n  }, [guideNameTimer]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"qadpt-createpopup\",\n    children: showPopup && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-title-sec\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: `${translate(\"Create\")} ${translate(name)}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-sub-title\",\n          children: `${translate(\"Type in the\")} ${translate(name)} ${translate(\"name and the URL of the webpage, where you want to create your\")} ${translate(name)}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 18\n        }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n          onClick: () => setShowPopup(false),\n          className: \"qadpt-closeicon\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          width: \"24\",\n          height: \"24\",\n          viewBox: \"0 0 50 50\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 20\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 18\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 16\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-createflds\",\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"guide-name\",\n                children: [translate(name), \" \", translate(\"Name\")]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"GuideName\",\n                required: true,\n                value: inputs.GuideName,\n                onChange: handleChange,\n                placeholder: name && `${translate(\"Enter\")} ${translate(name)} ${translate(\"Name\")}`,\n                helperText: errors.GuideName,\n                variant: \"outlined\",\n                error: !!errors.GuideName,\n                inputProps: {\n                  maxLength: 50\n                },\n                className: \"qadpt-createfield\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 24\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 22\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 20\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"target-url\",\n                children: translate(\"Target URL\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                id: \"target-url\",\n                name: \"TargetUrl\",\n                required: true,\n                value: inputs.TargetUrl,\n                onChange: handleChange,\n                placeholder: \"E.g., https://example.com\",\n                helperText: errors.TargetUrl,\n                variant: \"outlined\",\n                error: !!errors.TargetUrl,\n                inputProps: {\n                  maxLength: 200\n                },\n                className: \"qadpt-createfield\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 24\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 22\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 20\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 18\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 16\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-save-btn\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          onClick: handleSubmitTour,\n          color: \"primary\",\n          disabled: !isValid,\n          sx: {\n            opacity: !isValid ? 0.5 : 1\n          },\n          children: translate(\"Create & Open in Builder\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 18\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 16\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 16\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 9\n  }, this);\n};\n_s(CreateNewPopup, \"c1iROTsMEchIwLfZbEXHU4oZm7g=\", false, function () {\n  return [useSnackbar, useAuth, useTranslation];\n});\n_c = CreateNewPopup;\nexport default CreateNewPopup;\nvar _c;\n$RefreshReg$(_c, \"CreateNewPopup\");", "map": {"version": 3, "names": ["React", "useState", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "useSnackbar", "useAuth", "saveGuide", "CheckGuideNameExists", "accountId", "useTranslation", "openGuideInBuilder", "jsxDEV", "_jsxDEV", "CreateNewPopup", "props", "_s", "setTourslist", "setLoading", "setShowPopup", "showPopup", "setTotalcount", "setActiveTab", "changeTab", "name", "openSnackbar", "userDetails", "inputs", "setInputs", "GuideName", "TargetUrl", "errors", "setErrors", "<PERSON><PERSON><PERSON><PERSON>", "setIsValid", "snackbarOpen", "setSnackbarOpen", "snackbarSeverity", "setSnackbarSeverity", "t", "translate", "guideNameTimer", "setGuideNameTimer", "handleChange", "event", "value", "target", "updatedInputs", "error", "clearTimeout", "length", "prev", "timer", "setTimeout", "trim", "urlPattern", "test", "guideNameValid", "urlValid", "handleSubmitTour", "preventDefault", "newErrors", "newGuide", "GuideId", "GuideType", "Name", "Content", "OrganizationId", "CreatedDate", "Date", "toISOString", "CreatedBy", "UserName", "UpdatedBy", "Frequency", "Segment", "AccountId", "GuideStatus", "GuideStep", "checkGuideNamevalidation", "response", "Success", "url", "guideId", "Data", "ErrorMessage", "Error", "message", "String", "handleSnackbarClose", "useEffect", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "xmlns", "width", "height", "viewBox", "d", "container", "spacing", "item", "xs", "fullWidth", "required", "htmlFor", "onChange", "placeholder", "helperText", "variant", "inputProps", "max<PERSON><PERSON><PERSON>", "id", "type", "color", "disabled", "sx", "opacity", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptWeb/src/components/common/CreateNewGuidePopup.tsx"], "sourcesContent": ["import React, { useState, ChangeEvent } from \"react\";\r\nimport { TextField, Button, Snackbar, Alert, Grid, Typography,FormControl } from \"@mui/material\";\r\nimport { useSnackbar } from \"../../SnackbarContext\";\r\nimport { useAuth } from \"../auth/AuthProvider\";\r\nimport { saveGuide,CheckGuideNameExists } from \"../../services/GuideService\";\r\nimport { accountId } from \"../adminMenu/AdminMenu\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { openGuideInBuilder } from '../../utils/openGuideInBuilder';\r\ninterface CreateNewPopupProps {\r\n    setTourslist: (tours: any) => void;\r\n    setLoading?: (loading: boolean) => void; \r\n    setShowPopup: (show: boolean) => void;\r\n    showPopup: boolean;\r\n    setTotalcount: (count: number) => void;\r\n    setActiveTab: (tab: number) => void;\r\n    activeTab: number;\r\n    changeTab: () => void;\r\n    name: string;\r\n}\r\n\r\nconst CreateNewPopup: React.FC<CreateNewPopupProps> = (props) => {\r\n    const {\r\n        setTourslist,\r\n        setLoading,\r\n        setShowPopup,\r\n        showPopup,\r\n        setTotalcount,\r\n        setActiveTab,\r\n        changeTab,\r\n        name\r\n    } = props;\r\n\r\n    const { openSnackbar } = useSnackbar();\r\n    const { userDetails } = useAuth();\r\n    const [inputs, setInputs] = useState({\r\n        GuideName: \"\",\r\n        TargetUrl: \"\",\r\n    });\r\n    const [errors, setErrors] = useState<Partial<Record<string, string>>>({});\r\n    const [isValid, setIsValid] = useState(false);\r\n\r\n    const [snackbarOpen, setSnackbarOpen] = useState(false);\r\n    const [snackbarSeverity, setSnackbarSeverity] = useState<\"success\" | \"error\">(\"success\");\r\n  const { t: translate } = useTranslation();\r\n    const [guideNameTimer, setGuideNameTimer] = useState<NodeJS.Timeout | null>(null);\r\n\r\n    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {\r\n      const { name, value } = event.target;\r\n      const updatedInputs = { ...inputs, [name]: value };\r\n      let error = \"\"; \r\n      if (name === \"GuideName\") {\r\n        if (guideNameTimer) clearTimeout(guideNameTimer);\r\n        if (value.length > 50) {\r\n          error = translate(\"Guide Name should not exceed 50 characters.\");\r\n          setErrors((prev) => ({ ...prev, [name]: error }));\r\n        } else {\r\n          setErrors((prev) => ({ ...prev, [name]: \"\" }));\r\n          const timer = setTimeout(() => {\r\n            if (value.trim().length < 3) {\r\n              setErrors((prev) => ({ ...prev, [name]: translate(\"Guide Name must be at least 3 characters.\")}));\r\n            }\r\n          }, 1000);\r\n          setGuideNameTimer(timer);\r\n        }\r\n      }\r\n      if (name === \"TargetUrl\") {\r\n          const urlPattern = /^(https?:\\/\\/)?([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}(\\/.*)?$/;\r\n          if (value.length > 200) {\r\n              error = translate(\"Target URL must be less than 200 characters.\");\r\n          } else if (/[^a-zA-Z0-9-._~:/?#[\\]@!$&'()*+,;=]/.test(value)) {\r\n              error = translate(\"Target URL contains invalid characters.\");\r\n          } else if (!urlPattern.test(value)) {\r\n              error = `${translate(\"Please enter a valid URL. E.g.\")}, https://example.com\"`;\r\n          }\r\n      }\r\n      setInputs(updatedInputs);\r\n      setErrors((prev) => ({ ...prev, [name]: error }));\r\n  \r\n      const guideNameValid =\r\n          updatedInputs.GuideName.trim().length >= 3 &&\r\n          updatedInputs.GuideName.trim().length <= 50 &&\r\n          !errors.GuideName &&\r\n          (name !== \"GuideName\" || !error); // ensure current field error is not skipped\r\n  \r\n      const urlValid =\r\n          updatedInputs.TargetUrl.trim().length > 0 &&\r\n          updatedInputs.TargetUrl.length <= 200 &&\r\n          !errors.TargetUrl &&\r\n          (name !== \"TargetUrl\" || !error);\r\n  \r\n      setIsValid(guideNameValid && urlValid);\r\n  };\r\n\r\n    const handleSubmitTour = async (event: any) => {\r\n        event.preventDefault();\r\n        const newErrors: Partial<Record<string, string>> = {};\r\n        let isValid = true;\r\n\r\n        if (!inputs.GuideName) {\r\n            newErrors.GuideName = translate(\"Guide Name is required.\");\r\n            isValid = false;\r\n        } else if (inputs.GuideName.length < 3) {\r\n            newErrors.GuideName = translate(\"Guide Name must be at least 3 characters.\");\r\n            isValid = false;\r\n        }\r\n\r\n        if (!inputs.TargetUrl) {\r\n            newErrors.TargetUrl = \"Target URL is required.\";\r\n            isValid = false;\r\n        } else if (!/^https?:\\/\\/\\S+\\.\\S+$/.test(inputs.TargetUrl)) {\r\n            newErrors.TargetUrl = \"Enter a valid URL.\";\r\n            isValid = false;\r\n        }\r\n\r\n        if (isValid) {\r\n            const newGuide = {\r\n                GuideId: \"\",\r\n                GuideType: name,\r\n                Name: inputs.GuideName,\r\n                Content: \"\",\r\n                OrganizationId: userDetails?.OrganizationId,\r\n                CreatedDate: new Date().toISOString(),\r\n                CreatedBy: userDetails?.UserName,\r\n                UpdatedBy: userDetails?.UserName,\r\n                TargetUrl: inputs.TargetUrl,\r\n                Frequency: \"One-time\",\r\n                Segment: \"All users\",\r\n                AccountId: accountId,\r\n                GuideStatus: \"Draft\",\r\n                GuideStep: [],\r\n            };\r\n\r\n            try {\r\n                if (newGuide.OrganizationId) {\r\n                    \r\n                    const checkGuideNamevalidation = await CheckGuideNameExists(newGuide.Name, newGuide.AccountId,name);\r\n                    if (checkGuideNamevalidation) {\r\n                        newErrors.GuideName = translate(\"Guide Name Already Exists\");\r\n                        setErrors(newErrors);\r\n                    }\r\n                    const response = await saveGuide(newGuide);\r\n                    if (response.Success) {\r\n                        openSnackbar(`${inputs.GuideName} ${name}  ${translate(\"created successfully. Please Check The Drafts\")}`, \"success\");\r\n                        let url = newGuide.TargetUrl;\r\n                        const guideId = response.Data;\r\n                        if (guideId) {\r\n                            openGuideInBuilder(newGuide.TargetUrl, guideId, newGuide.AccountId);\r\n                        }\r\n                        changeTab(); \r\n                        setActiveTab(2)\r\n                        setShowPopup(false);\r\n                    } else {\r\n                        openSnackbar(translate(response.ErrorMessage) || translate(\"Failed to create guide\"), \"error\");\r\n                    }\r\n                }\r\n            } catch (error) {\r\n                openSnackbar(`${translate(\"An unexpected error occurred\")}: ${(error instanceof Error ? error.message : String(error))}`, \"error\");\r\n            } finally {\r\n                setSnackbarOpen(true);\r\n            }\r\n        } else {\r\n            setErrors(newErrors);\r\n        }\r\n    };\r\n\r\n    const handleSnackbarClose = () => {\r\n        setSnackbarOpen(false);\r\n    };\r\n\r\n    React.useEffect(() => {\r\n      return () => {\r\n        if (guideNameTimer) clearTimeout(guideNameTimer);\r\n      };\r\n    }, [guideNameTimer]);\r\n\r\n    return (\r\n        <div className=\"qadpt-createpopup\">\r\n            {showPopup && (\r\n               <div>\r\n               <div className=\"qadpt-title-sec\">\r\n              <div className=\"qadpt-title\">{`${translate(\"Create\")} ${translate(name)}`}</div>\r\n                 <div className=\"qadpt-sub-title\">\r\n                {`${translate(\"Type in the\")} ${translate(name)} ${translate(\"name and the URL of the webpage, where you want to create your\")} ${translate(name)}`}\r\n                 </div>\r\n                 <svg\r\n                   onClick={() => setShowPopup(false)}\r\n                   className=\"qadpt-closeicon\"\r\n                   xmlns=\"http://www.w3.org/2000/svg\"\r\n                   width=\"24\"\r\n                   height=\"24\"\r\n                   viewBox=\"0 0 50 50\"\r\n                 >\r\n                   <path d=\"M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z\" />\r\n                 </svg>\r\n               </div>\r\n               <div className=\"qadpt-createflds\">\r\n                 <Grid container spacing={2}>\r\n                   <Grid item xs={12}>\r\n                     <FormControl fullWidth required>\r\n                    <label htmlFor=\"guide-name\">{translate(name)} {translate(\"Name\")}</label>\r\n                       <TextField\r\n                         name=\"GuideName\"\r\n                         required\r\n                         value={inputs.GuideName}\r\n                         onChange={handleChange}\r\n                      placeholder={name && `${translate(\"Enter\")} ${translate(name)} ${translate(\"Name\")}`}\r\n                         helperText={errors.GuideName}\r\n                         variant=\"outlined\"\r\n                         error={!!errors.GuideName}\r\n                         inputProps={{ maxLength: 50 }}\r\n                         className=\"qadpt-createfield\"\r\n                       />\r\n                     </FormControl>\r\n                   </Grid>\r\n                   <Grid item xs={12}>\r\n                     <FormControl fullWidth required>\r\n                    <label htmlFor=\"target-url\">{translate(\"Target URL\")}</label>\r\n                       <TextField\r\n                         id=\"target-url\"\r\n                         name=\"TargetUrl\"\r\n                         required\r\n                         value={inputs.TargetUrl}\r\n                         onChange={handleChange}\r\n                         placeholder=\"E.g., https://example.com\"\r\n                         helperText={errors.TargetUrl}\r\n                         variant=\"outlined\"\r\n                         error={!!errors.TargetUrl}\r\n                         inputProps={{ maxLength: 200 }}\r\n                         className=\"qadpt-createfield\"\r\n                       />\r\n                     </FormControl>\r\n                   </Grid>\r\n                 </Grid>\r\n               </div>\r\n               <div className=\"qadpt-save-btn\">\r\n                 <Button\r\n                   type=\"submit\"\r\n                   variant=\"contained\"\r\n                   onClick={handleSubmitTour}\r\n                   color=\"primary\"\r\n                   disabled={!isValid}\r\n                    sx={{\r\n                      opacity: !isValid ? 0.5 : 1,\r\n                    }}\r\n                 >\r\n                {translate(\"Create & Open in Builder\")}\r\n                 </Button>\r\n               </div>\r\n             </div>\r\n             \r\n            )}\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default CreateNewPopup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAqB,OAAO;AACpD,SAASC,SAAS,EAAEC,MAAM,EAAmBC,IAAI,EAAaC,WAAW,QAAQ,eAAe;AAChG,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,SAAS,EAACC,oBAAoB,QAAQ,6BAA6B;AAC5E,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,kBAAkB,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAapE,MAAMC,cAA6C,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC7D,MAAM;IACFC,YAAY;IACZC,UAAU;IACVC,YAAY;IACZC,SAAS;IACTC,aAAa;IACbC,YAAY;IACZC,SAAS;IACTC;EACJ,CAAC,GAAGT,KAAK;EAET,MAAM;IAAEU;EAAa,CAAC,GAAGpB,WAAW,CAAC,CAAC;EACtC,MAAM;IAAEqB;EAAY,CAAC,GAAGpB,OAAO,CAAC,CAAC;EACjC,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAC;IACjC6B,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAkC,CAAC,CAAC,CAAC;EACzE,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAsB,SAAS,CAAC;EAC1F,MAAM;IAAEuC,CAAC,EAAEC;EAAU,CAAC,GAAG9B,cAAc,CAAC,CAAC;EACvC,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAwB,IAAI,CAAC;EAEjF,MAAM2C,YAAY,GAAIC,KAAoC,IAAK;IAC7D,MAAM;MAAEpB,IAAI;MAAEqB;IAAM,CAAC,GAAGD,KAAK,CAACE,MAAM;IACpC,MAAMC,aAAa,GAAG;MAAE,GAAGpB,MAAM;MAAE,CAACH,IAAI,GAAGqB;IAAM,CAAC;IAClD,IAAIG,KAAK,GAAG,EAAE;IACd,IAAIxB,IAAI,KAAK,WAAW,EAAE;MACxB,IAAIiB,cAAc,EAAEQ,YAAY,CAACR,cAAc,CAAC;MAChD,IAAII,KAAK,CAACK,MAAM,GAAG,EAAE,EAAE;QACrBF,KAAK,GAAGR,SAAS,CAAC,6CAA6C,CAAC;QAChER,SAAS,CAAEmB,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAAC3B,IAAI,GAAGwB;QAAM,CAAC,CAAC,CAAC;MACnD,CAAC,MAAM;QACLhB,SAAS,CAAEmB,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAAC3B,IAAI,GAAG;QAAG,CAAC,CAAC,CAAC;QAC9C,MAAM4B,KAAK,GAAGC,UAAU,CAAC,MAAM;UAC7B,IAAIR,KAAK,CAACS,IAAI,CAAC,CAAC,CAACJ,MAAM,GAAG,CAAC,EAAE;YAC3BlB,SAAS,CAAEmB,IAAI,KAAM;cAAE,GAAGA,IAAI;cAAE,CAAC3B,IAAI,GAAGgB,SAAS,CAAC,2CAA2C;YAAC,CAAC,CAAC,CAAC;UACnG;QACF,CAAC,EAAE,IAAI,CAAC;QACRE,iBAAiB,CAACU,KAAK,CAAC;MAC1B;IACF;IACA,IAAI5B,IAAI,KAAK,WAAW,EAAE;MACtB,MAAM+B,UAAU,GAAG,uDAAuD;MAC1E,IAAIV,KAAK,CAACK,MAAM,GAAG,GAAG,EAAE;QACpBF,KAAK,GAAGR,SAAS,CAAC,8CAA8C,CAAC;MACrE,CAAC,MAAM,IAAI,qCAAqC,CAACgB,IAAI,CAACX,KAAK,CAAC,EAAE;QAC1DG,KAAK,GAAGR,SAAS,CAAC,yCAAyC,CAAC;MAChE,CAAC,MAAM,IAAI,CAACe,UAAU,CAACC,IAAI,CAACX,KAAK,CAAC,EAAE;QAChCG,KAAK,GAAG,GAAGR,SAAS,CAAC,gCAAgC,CAAC,wBAAwB;MAClF;IACJ;IACAZ,SAAS,CAACmB,aAAa,CAAC;IACxBf,SAAS,CAAEmB,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAAC3B,IAAI,GAAGwB;IAAM,CAAC,CAAC,CAAC;IAEjD,MAAMS,cAAc,GAChBV,aAAa,CAAClB,SAAS,CAACyB,IAAI,CAAC,CAAC,CAACJ,MAAM,IAAI,CAAC,IAC1CH,aAAa,CAAClB,SAAS,CAACyB,IAAI,CAAC,CAAC,CAACJ,MAAM,IAAI,EAAE,IAC3C,CAACnB,MAAM,CAACF,SAAS,KAChBL,IAAI,KAAK,WAAW,IAAI,CAACwB,KAAK,CAAC,CAAC,CAAC;;IAEtC,MAAMU,QAAQ,GACVX,aAAa,CAACjB,SAAS,CAACwB,IAAI,CAAC,CAAC,CAACJ,MAAM,GAAG,CAAC,IACzCH,aAAa,CAACjB,SAAS,CAACoB,MAAM,IAAI,GAAG,IACrC,CAACnB,MAAM,CAACD,SAAS,KAChBN,IAAI,KAAK,WAAW,IAAI,CAACwB,KAAK,CAAC;IAEpCd,UAAU,CAACuB,cAAc,IAAIC,QAAQ,CAAC;EAC1C,CAAC;EAEC,MAAMC,gBAAgB,GAAG,MAAOf,KAAU,IAAK;IAC3CA,KAAK,CAACgB,cAAc,CAAC,CAAC;IACtB,MAAMC,SAA0C,GAAG,CAAC,CAAC;IACrD,IAAI5B,OAAO,GAAG,IAAI;IAElB,IAAI,CAACN,MAAM,CAACE,SAAS,EAAE;MACnBgC,SAAS,CAAChC,SAAS,GAAGW,SAAS,CAAC,yBAAyB,CAAC;MAC1DP,OAAO,GAAG,KAAK;IACnB,CAAC,MAAM,IAAIN,MAAM,CAACE,SAAS,CAACqB,MAAM,GAAG,CAAC,EAAE;MACpCW,SAAS,CAAChC,SAAS,GAAGW,SAAS,CAAC,2CAA2C,CAAC;MAC5EP,OAAO,GAAG,KAAK;IACnB;IAEA,IAAI,CAACN,MAAM,CAACG,SAAS,EAAE;MACnB+B,SAAS,CAAC/B,SAAS,GAAG,yBAAyB;MAC/CG,OAAO,GAAG,KAAK;IACnB,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAACuB,IAAI,CAAC7B,MAAM,CAACG,SAAS,CAAC,EAAE;MACxD+B,SAAS,CAAC/B,SAAS,GAAG,oBAAoB;MAC1CG,OAAO,GAAG,KAAK;IACnB;IAEA,IAAIA,OAAO,EAAE;MACT,MAAM6B,QAAQ,GAAG;QACbC,OAAO,EAAE,EAAE;QACXC,SAAS,EAAExC,IAAI;QACfyC,IAAI,EAAEtC,MAAM,CAACE,SAAS;QACtBqC,OAAO,EAAE,EAAE;QACXC,cAAc,EAAEzC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyC,cAAc;QAC3CC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACrCC,SAAS,EAAE7C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE8C,QAAQ;QAChCC,SAAS,EAAE/C,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE8C,QAAQ;QAChC1C,SAAS,EAAEH,MAAM,CAACG,SAAS;QAC3B4C,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAE,WAAW;QACpBC,SAAS,EAAEnE,SAAS;QACpBoE,WAAW,EAAE,OAAO;QACpBC,SAAS,EAAE;MACf,CAAC;MAED,IAAI;QACA,IAAIhB,QAAQ,CAACK,cAAc,EAAE;UAEzB,MAAMY,wBAAwB,GAAG,MAAMvE,oBAAoB,CAACsD,QAAQ,CAACG,IAAI,EAAEH,QAAQ,CAACc,SAAS,EAACpD,IAAI,CAAC;UACnG,IAAIuD,wBAAwB,EAAE;YAC1BlB,SAAS,CAAChC,SAAS,GAAGW,SAAS,CAAC,2BAA2B,CAAC;YAC5DR,SAAS,CAAC6B,SAAS,CAAC;UACxB;UACA,MAAMmB,QAAQ,GAAG,MAAMzE,SAAS,CAACuD,QAAQ,CAAC;UAC1C,IAAIkB,QAAQ,CAACC,OAAO,EAAE;YAClBxD,YAAY,CAAC,GAAGE,MAAM,CAACE,SAAS,IAAIL,IAAI,KAAKgB,SAAS,CAAC,+CAA+C,CAAC,EAAE,EAAE,SAAS,CAAC;YACrH,IAAI0C,GAAG,GAAGpB,QAAQ,CAAChC,SAAS;YAC5B,MAAMqD,OAAO,GAAGH,QAAQ,CAACI,IAAI;YAC7B,IAAID,OAAO,EAAE;cACTxE,kBAAkB,CAACmD,QAAQ,CAAChC,SAAS,EAAEqD,OAAO,EAAErB,QAAQ,CAACc,SAAS,CAAC;YACvE;YACArD,SAAS,CAAC,CAAC;YACXD,YAAY,CAAC,CAAC,CAAC;YACfH,YAAY,CAAC,KAAK,CAAC;UACvB,CAAC,MAAM;YACHM,YAAY,CAACe,SAAS,CAACwC,QAAQ,CAACK,YAAY,CAAC,IAAI7C,SAAS,CAAC,wBAAwB,CAAC,EAAE,OAAO,CAAC;UAClG;QACJ;MACJ,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACZvB,YAAY,CAAC,GAAGe,SAAS,CAAC,8BAA8B,CAAC,KAAMQ,KAAK,YAAYsC,KAAK,GAAGtC,KAAK,CAACuC,OAAO,GAAGC,MAAM,CAACxC,KAAK,CAAC,EAAG,EAAE,OAAO,CAAC;MACtI,CAAC,SAAS;QACNZ,eAAe,CAAC,IAAI,CAAC;MACzB;IACJ,CAAC,MAAM;MACHJ,SAAS,CAAC6B,SAAS,CAAC;IACxB;EACJ,CAAC;EAED,MAAM4B,mBAAmB,GAAGA,CAAA,KAAM;IAC9BrD,eAAe,CAAC,KAAK,CAAC;EAC1B,CAAC;EAEDrC,KAAK,CAAC2F,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACX,IAAIjD,cAAc,EAAEQ,YAAY,CAACR,cAAc,CAAC;IAClD,CAAC;EACH,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EAEpB,oBACI5B,OAAA;IAAK8E,SAAS,EAAC,mBAAmB;IAAAC,QAAA,EAC7BxE,SAAS,iBACPP,OAAA;MAAA+E,QAAA,gBACA/E,OAAA;QAAK8E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBACjC/E,OAAA;UAAK8E,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAE,GAAGpD,SAAS,CAAC,QAAQ,CAAC,IAAIA,SAAS,CAAChB,IAAI,CAAC;QAAE;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7EnF,OAAA;UAAK8E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAChC,GAAGpD,SAAS,CAAC,aAAa,CAAC,IAAIA,SAAS,CAAChB,IAAI,CAAC,IAAIgB,SAAS,CAAC,gEAAgE,CAAC,IAAIA,SAAS,CAAChB,IAAI,CAAC;QAAE;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7I,CAAC,eACNnF,OAAA;UACEoF,OAAO,EAAEA,CAAA,KAAM9E,YAAY,CAAC,KAAK,CAAE;UACnCwE,SAAS,EAAC,iBAAiB;UAC3BO,KAAK,EAAC,4BAA4B;UAClCC,KAAK,EAAC,IAAI;UACVC,MAAM,EAAC,IAAI;UACXC,OAAO,EAAC,WAAW;UAAAT,QAAA,eAEnB/E,OAAA;YAAMyF,CAAC,EAAC;UAA+M;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNnF,OAAA;QAAK8E,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B/E,OAAA,CAACV,IAAI;UAACoG,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAZ,QAAA,gBACzB/E,OAAA,CAACV,IAAI;YAACsG,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAd,QAAA,eAChB/E,OAAA,CAACT,WAAW;cAACuG,SAAS;cAACC,QAAQ;cAAAhB,QAAA,gBAChC/E,OAAA;gBAAOgG,OAAO,EAAC,YAAY;gBAAAjB,QAAA,GAAEpD,SAAS,CAAChB,IAAI,CAAC,EAAC,GAAC,EAACgB,SAAS,CAAC,MAAM,CAAC;cAAA;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtEnF,OAAA,CAACZ,SAAS;gBACRuB,IAAI,EAAC,WAAW;gBAChBoF,QAAQ;gBACR/D,KAAK,EAAElB,MAAM,CAACE,SAAU;gBACxBiF,QAAQ,EAAEnE,YAAa;gBAC1BoE,WAAW,EAAEvF,IAAI,IAAI,GAAGgB,SAAS,CAAC,OAAO,CAAC,IAAIA,SAAS,CAAChB,IAAI,CAAC,IAAIgB,SAAS,CAAC,MAAM,CAAC,EAAG;gBAClFwE,UAAU,EAAEjF,MAAM,CAACF,SAAU;gBAC7BoF,OAAO,EAAC,UAAU;gBAClBjE,KAAK,EAAE,CAAC,CAACjB,MAAM,CAACF,SAAU;gBAC1BqF,UAAU,EAAE;kBAAEC,SAAS,EAAE;gBAAG,CAAE;gBAC9BxB,SAAS,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACPnF,OAAA,CAACV,IAAI;YAACsG,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAd,QAAA,eAChB/E,OAAA,CAACT,WAAW;cAACuG,SAAS;cAACC,QAAQ;cAAAhB,QAAA,gBAChC/E,OAAA;gBAAOgG,OAAO,EAAC,YAAY;gBAAAjB,QAAA,EAAEpD,SAAS,CAAC,YAAY;cAAC;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1DnF,OAAA,CAACZ,SAAS;gBACRmH,EAAE,EAAC,YAAY;gBACf5F,IAAI,EAAC,WAAW;gBAChBoF,QAAQ;gBACR/D,KAAK,EAAElB,MAAM,CAACG,SAAU;gBACxBgF,QAAQ,EAAEnE,YAAa;gBACvBoE,WAAW,EAAC,2BAA2B;gBACvCC,UAAU,EAAEjF,MAAM,CAACD,SAAU;gBAC7BmF,OAAO,EAAC,UAAU;gBAClBjE,KAAK,EAAE,CAAC,CAACjB,MAAM,CAACD,SAAU;gBAC1BoF,UAAU,EAAE;kBAAEC,SAAS,EAAE;gBAAI,CAAE;gBAC/BxB,SAAS,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnF,OAAA;QAAK8E,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B/E,OAAA,CAACX,MAAM;UACLmH,IAAI,EAAC,QAAQ;UACbJ,OAAO,EAAC,WAAW;UACnBhB,OAAO,EAAEtC,gBAAiB;UAC1B2D,KAAK,EAAC,SAAS;UACfC,QAAQ,EAAE,CAACtF,OAAQ;UAClBuF,EAAE,EAAE;YACFC,OAAO,EAAE,CAACxF,OAAO,GAAG,GAAG,GAAG;UAC5B,CAAE;UAAA2D,QAAA,EAELpD,SAAS,CAAC,0BAA0B;QAAC;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAEL;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAChF,EAAA,CAzOIF,cAA6C;EAAA,QAYtBT,WAAW,EACZC,OAAO,EAURI,cAAc;AAAA;AAAAgH,EAAA,GAvBnC5G,cAA6C;AA2OnD,eAAeA,cAAc;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}