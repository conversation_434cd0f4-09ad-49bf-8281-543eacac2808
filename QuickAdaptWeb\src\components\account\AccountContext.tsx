import React, { createContext, useState, ReactNode } from 'react';

// Create the context
export const AccountContext = createContext<{
  accountId: string;
  setAccountId: (id: string) => void;
  roles: string[];
  setRoles: (roles: [])=>void;
}>({
  accountId: '',      // Default value
  setAccountId: () => { },  // Empty function as a placeholder
  roles: [],
  setRoles: () =>[],
});

// Provider component
export const AccountProvider = ({ children }: { children: ReactNode }) => {
  const [accountId, setAccountId] = useState<string>(''); // Store accountId
  const [roles, setRoles] = useState<string[]>([]);

  return (
    <AccountContext.Provider value={{ accountId, setAccountId,roles,setRoles }}>
      {children}
    </AccountContext.Provider>
  );
};
