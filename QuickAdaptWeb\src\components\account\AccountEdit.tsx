import React, { useState, useEffect, ChangeEvent } from "react";
import { getAllOrganizations } from "../../services/OrganizationService";
import { SubmitAccountDetails, fetchAccountsById } from "../../services/AccountService";
import { 
	TextField, 
	Button, 
  Radio,
  RadioGroup,
  FormControlLabel
} from "@mui/material";
import { useSnackbar } from "../../SnackbarContext";
import { useAuth } from "../auth/AuthProvider";
import { User } from "../../models/User";
import { useTranslation } from "react-i18next";

type InputFields = {
	AccountName: string;
	DomainUrl: string;
};
type ErrorFields = Partial<InputFields>;

const EditAccount = (props: any) => {
	const {
		showEditPopup,
		setShowEditPopup,
		accountidedit,
		setModels,
		setLoading,
		setTotalcount,
		orderByField,
		filters,
	} = props;
	const [organizations, setOrganizations] = useState<any[]>([]);
	const [snackbarOpen, setSnackbarOpen] = useState(false);
	const [snackbarMessage, setSnackbarMessage] = useState("");
	const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">("success");
	const [skip] = useState("0");
	const [top] = useState("30");
	const { openSnackbar } = useSnackbar();
	const { userDetails } = useAuth();
	const [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId ?? "");
	const [, setUser] = useState<User | null>(null);
	const [isValid, setIsValid] = useState(true);
	const [hasChanges, setHasChanges] = useState(false);
	//const [totalcount, setTotalcount] = useState(0);
	// const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
	// 	page: 0,
	// 	pageSize: 10,
	// });
	const [errors, setErrors] = useState<ErrorFields>({
		AccountName: "",
		DomainUrl: ""
	});

	const [originalAccountDetails, setOriginalAccountDetails] = useState({
		AccountId: "",
		AccountName: "",
		AccountType: "",
		CreatedBy: "",
		CreatedDate: "",
		OrganizationId: "",
		UpdatedBy: userDetails?.UserName,
		UpdatedDate: "",
		Active: Boolean(true),
		DomainUrl: "",
		Rtl: false
	});

	const [AccountDetails, setAccountDetails] = useState({
		AccountId: "",
		AccountName: "",
		AccountType: "",
		CreatedBy: "",
		CreatedDate: "",
		OrganizationId: "",
		UpdatedBy: userDetails?.UserName,
		UpdatedDate: "",
		Active: Boolean(true),
		DomainUrl: "",
		Rtl: false
	});

	const { t: translate } = useTranslation();

	useEffect(() => {
		if (showEditPopup) {
			fetchAccountDetails(accountidedit);
		}
	}, [showEditPopup]);
	// Validate inputs whenever AccountDetails change
	useEffect(() => {
		const newErrors: ErrorFields = {};

		// Account Name Validation
		if (AccountDetails.AccountName) {
			if (AccountDetails.AccountName.length < 5 || !AccountDetails.AccountName.trim()) {
				newErrors.AccountName = translate("Account Name must be at least 5 characters.");
			} else if (/[^a-zA-Z\s]/g.test(AccountDetails.AccountName)) {
				newErrors.AccountName = translate("Account Name can only contain letters and spaces.");
			} else {
				newErrors.AccountName = "";
			}
		}

		// Domain URL Validation
		if (AccountDetails.DomainUrl) {
			const domainPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/)?$/;
			if (!domainPattern.test(AccountDetails.DomainUrl.trim())) {
				newErrors.DomainUrl = translate("Please enter a valid full domain URL (e.g., https://example.com).");
			} else {
				newErrors.DomainUrl = "";
			}
		}

		setErrors(prev => ({ ...prev, ...newErrors }));
	}, [AccountDetails]);

	useEffect(() => {
		const userInfoString = localStorage.getItem("userInfo");	
		if (userInfoString) { 
			try {
				const userInfo = JSON.parse(userInfoString);	
				if (userInfo['user']) {
					const parsedUser = JSON.parse(userInfo['user']);
					setUser(parsedUser);	
					if (parsedUser) {
						const OrgId = parsedUser.OrganizationId ?? '';
						setOrganizationId(OrgId);
					}
				}
			} catch (error) {
				console.error("Error parsing userInfo: ", error);
			}
		}
		else if (userDetails) {
			setUser(userDetails);	
			if (userDetails) {
				const OrgId = userDetails.OrganizationId ?? '';
				setOrganizationId(OrgId);
			}
		}
	}, []);


	// Function to check if current account details differ from original data
	const checkForChanges = (currentDetails: any) => {
		const fieldsToCompare = ['AccountName', 'DomainUrl', 'Rtl'] as const;
		return fieldsToCompare.some(field => currentDetails[field] !== originalAccountDetails[field]);
	};

	const fetchAccountDetails = async (id: any) => {
		try {
			const responseData = await fetchAccountsById(id);

			if (!responseData) {
				throw new Error("Network response was not ok");
			}

			const accountData = {
				AccountId: responseData.AccountId,
				AccountName: responseData.AccountName,
				AccountType: responseData.AccountType,
				CreatedBy: responseData.CreatedBy,
				CreatedDate: responseData.CreatedDate,
				OrganizationId: responseData.OrganizationId,
				UpdatedBy: userDetails?.UserName,
				UpdatedDate: new Date().toUTCString(),
				Active: true,
				DomainUrl: responseData.DomainUrl,
				Rtl: responseData.Rtl || false
			};

			// Set both original and current data
			setOriginalAccountDetails(accountData);
			setAccountDetails(accountData);

			// Reset change tracking
			setHasChanges(false);
		} catch (error) {
			console.error("Failed to fetch user details:", error);
		}
	};
	const handleOrganizationDropdownOpen = async () => {
		try {
			const response = await getAllOrganizations(setOrganizations, setLoading);
		} catch (error) {
			console.error("Error fetching organizations:", error);
		}
	};
	const alphanumericRegex = /^[a-zA-Z0-9]*$/;
	const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
		const { name, value } = event.target;
		let error = "";
		let processedValue = value.trimStart();

		if (name === "AccountType") {
			const selectedOrganization = organizations.find((org) => org.Name === value);
			if (selectedOrganization) {
				const newAccountDetails = {
					...AccountDetails,
					AccountType: value,
					OrganizationId: OrganizationId
				};
				setAccountDetails(newAccountDetails);
				setHasChanges(checkForChanges(newAccountDetails));
				return;
			}
		} else if (name === "AccountName") {
			// Allow only letters and spaces, remove special characters, numbers, and trim leading spaces
			processedValue = value.replace(/[^a-zA-Z\s]/g, "").replace(/^\s+/, "");

			// Check if the length is less than 5 characters or if the input is only spaces
			if (processedValue.length < 5 || !processedValue.trim()) {
				error = translate("Minimum 3 characters.");
			}
		} else if (name === "DomainUrl") {
			const domainPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/)?$/;
			if (!domainPattern.test(value.trim())) {
				error = translate("Please enter a valid full domain URL (e.g., https://example.com).");
			}
			processedValue = value.trim();
		}

		const newAccountDetails = { ...AccountDetails, [name]: processedValue };
		setAccountDetails(newAccountDetails);
		setErrors((prev) => ({ ...prev, [name]: error }));

		// Check for changes after updating account details
		setHasChanges(checkForChanges(newAccountDetails));
	};

	// const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
	// 	const { name, value } = event.target;
	// 	const alphanumericWithSpacesRegex = /^[a-zA-Z0-9\s]*$/; // Allow letters, numbers, and spaces
	// 	let error = "";
	// 	let processedValue = value;

	// 	if (name === "AccountType") {
	// 		const selectedOrganization = organizations.find((org) => org.Name === value);
	// 		if (selectedOrganization) {
	// 			setAccountDetails((values) => ({
	// 				...values,
	// 				AccountType: value,
	// 				OrganizationId: "********-*********-134dc53c-f123-4655-aa39-0529fa976863", // Placeholder ID
	// 			}));
	// 		}
	// 	} else if (name === "AccountName") {
	// 		// Remove special characters
	// 		processedValue = value.replace(/[^a-zA-Z0-9\s]/g, "");
	// 	}

	// 	setAccountDetails((prev) => ({ ...prev, [name]: processedValue }));

	// 	if (name === "AccountName" && processedValue.length < 5) {
	// 		error = "Account Name must be at least 5 characters.";
	// 	} else {
	// 		setAccountDetails((values) => ({ ...values, [name]: value }));
	// 	}
	// 	setErrors((prev) => ({ ...prev, [name]: error }));
	// };

	const handleSubmit = async (e: any) => {
		e.preventDefault();
		const newErrors: ErrorFields = {};

		(Object.keys(AccountDetails) as (keyof InputFields)[]).forEach((key) => {
			if (!AccountDetails[key]) {
				// newErrors[key] = `${key} is required.`;
				// isValid = false;
			} else if (key === "AccountName") {
				if (AccountDetails[key].length < 5) {
					newErrors[key] = translate("Account Name must be at least 5 characters.");
					setIsValid(false);
				}
				if (/[^a-zA-Z\s]/g.test(AccountDetails[key])) {
					newErrors[key] = translate("Account Name can only contain letters and numbers.");
					setIsValid(false);
				}
			}
		});

		// Validate AccountName
		if (!AccountDetails.AccountName) {
			setSnackbarMessage(translate("Account Name is required."));
			setSnackbarSeverity("error");
			setSnackbarOpen(true);
			return;
		}
		if (!AccountDetails.DomainUrl) {
			setSnackbarMessage(translate("Domain Url is required."));
			setSnackbarSeverity("error");
			setSnackbarOpen(true);
			return;
		}

		// If validation passes, prepare the new inputs
		// const newAccountDetails = {
		// 	...AccountDetails,
		// 	// Add any additional fields or transformations if needed
		// };

		setErrors(newErrors);
		if (isValid) {
			setLoading(true);
			try {
				await SubmitAccountDetails(
					setLoading,
					setModels,
					setShowEditPopup,
					AccountDetails,
					OrganizationId,
					skip,
					top,
					setTotalcount,
					openSnackbar,
					orderByField,
					filters
				);

			} catch (error) {

			}
		}
	};
	const handleSnackbarClose = () => {
		setSnackbarOpen(false);
	};

	return (
		showEditPopup && (
			<div className="qadpt-modal-overlay">
			<div
				className="qadpt-accounteditpopup"
			>
				  <div className="qadpt-title-sec">
				<div className="qadpt-title">{translate("Edit Account")}</div>
					<svg
						onClick={() => setShowEditPopup(false)}
						className="qadpt-closeicon"
						xmlns="http://www.w3.org/2000/svg"
						x="0px"
						y="0px"
						width="24"
						height="24"
						viewBox="0 0 50 50"
					>
						<path d="M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z"></path>
					</svg>
				</div>
				<form onSubmit={handleSubmit}>
						<div className="qadpt-accountcreatefield">
							<label htmlFor="edit-account-name">{translate("Account Name")}</label>
							<TextField
								id="edit-account-name"
								name="AccountName"
								value={AccountDetails.AccountName}
								onChange={handleChange}
								placeholder={translate("Enter Account Name")}
								helperText={errors.AccountName && translate(errors.AccountName)}
								variant="outlined"
								error={!!errors.AccountName}
								inputProps={{ maxLength: 50 }}
								className={`qadpt-acctfield ${errors.AccountName ? 'qadpt-error' : ''}`}
							/>
						</div>
						<div className="qadpt-accountcreatefield">
							<label htmlFor="edit-account-domain">{translate("Domain Url")}</label>
							<TextField
								id="edit-account-domain"
								name="DomainUrl"
								value={AccountDetails.DomainUrl}
								onChange={handleChange}
								placeholder={translate("Enter Domain Url")}
								helperText={errors.DomainUrl && translate(errors.DomainUrl)}
								variant="outlined"
								error={!!errors.DomainUrl}
								inputProps={{ maxLength: 50 }}
								className={`qadpt-acctfield ${errors.DomainUrl ? 'qadpt-error' : ''}`}
							/>
						</div>

						<div className="qadpt-accountcreatefield">
							<label>{translate("Text Direction")}</label>
							<RadioGroup
								row
								name="Rtl"
								value={AccountDetails.Rtl ? "rtl" : "ltr"}
								onChange={(e) => {
									const newValue = e.target.value === "rtl";
									setAccountDetails(prev => ({
										...prev,
										Rtl: newValue
									}));
						// Check for changes after updating
									setHasChanges(checkForChanges({ ...AccountDetails, Rtl: newValue }));
								}}
							>
								<FormControlLabel value="ltr" control={<Radio />} label="LTR" />
								<FormControlLabel value="rtl" control={<Radio />} label="RTL" />
							</RadioGroup>
						</div>

						<div className="qadpt-account-buttons">
						<Button
							className="qadpt-save-btn"
							type="submit"
							disabled={!hasChanges || Object.values(errors).some(error => error !== "")}
							sx={{
								'&.Mui-disabled': {
									opacity: 0.5
								}
							}}
						>
							{translate("Save")}
						</Button>
  </div>
</form>

				{/* <Snackbar
					style={{ zIndex: "9999", position: "relative", top: "-500px", width: "500px", right: "-600px" }}
					open={snackbarOpen}
					autoHideDuration={8000}
					onClose={handleSnackbarClose}
					anchorOrigin={{ vertical: "top", horizontal: "right" }}
				>
					<Alert
						onClose={handleSnackbarClose}
						severity={snackbarSeverity}
						sx={{ width: "100%" }}
					>
						{snackbarMessage}
					</Alert>
				</Snackbar>{" "} */}
				</div>
		</div>
		)
	);
};

export default EditAccount;
