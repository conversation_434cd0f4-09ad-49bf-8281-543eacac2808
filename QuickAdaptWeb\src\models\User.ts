
export interface User {
	FirstName: string;
	LastName: string;
	UserName: string;
	EmailId: string;
	Password: string;
	ContactNumber: string;
	Gender: string;
	DateofBirth: string;
	AdminDeactivated: boolean;
	EmailConfirmed: Boolean;
	LoginType: string;
	ProfilePhoto: string;
	RTL: Boolean;
	TimeZone: string;
	UserId: string;
	UserType: string;
	OrganizationId: string;
	Language?: string; // User's preferred language
	AccountId?: string; // User's account ID
	IsActive?: boolean; // Whether user is active
	IsBlocked?: boolean; // Whether user is blocked
	InvalidAttempts?: number; // Number of invalid login attempts
	VCode?: string; // Verification code
	CreatedDate?: string; // Creation date
	UpdatedDate?: string; // Last update date
  }