import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
    GridColDef,
    GridToolbarContainer,
    GridToolbarColumnsButton,
    GridToolbarFilterButton,
    GridToolbarDensitySelector,
} from "@mui/x-data-grid";
import {
    Button,
    Menu,
    MenuItem,
    FormControlLabel,
    IconButton,
    Link,
    Dialog,
    DialogActions,
    DialogTitle,
    DialogContent,
    Box,
    Card,
    Snackbar,
    Alert,
    Tooltip,
    CardContent,
    CardActions,
    CardMedia,
    Typography,
    Modal
} from "@mui/material";
import {
    Edit as EditIcon,
    Delete as DeleteIcon,
    SaveAlt as SaveAltIcon,
    Opacity,
    FileUpload,
} from "@mui/icons-material";
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';
import loader from "../../assets/loader.gif";
import CustomGrid from "../common/Grid";
import { getAllFiles,GetGuidesUsedByFile } from "../../services/FileManagementService";
import { isSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import AddIcon from '@mui/icons-material/Add';
import { UploadImage,DeleteFile ,ReplaceFile} from "../../services/FileManagementService";
import { height } from "@mui/system";
import { inherits } from "util";
import LinkIcon from '@mui/icons-material/Link';
import UpgradeIcon from '@mui/icons-material/Upgrade';
import { DeleteRed, PreviewImage, Replace, copy } from "../../assets/icons/icons";


interface FileUpload {
    ImageId: string;
    FileName: string | null;
    Url: string;
}

const FileList: React.FC = () => {
    const [models, setModels] = useState<FileUpload[]>([]);
    const [loading, setLoading] = useState(false);
    const [emailiddelete, setemailiddelete] = useState("");
    const [useridedit, setUserIdEdit] = useState("");
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [sidebarOpen, setSidebarOpen] = useState(isSidebarOpen());
    const [showPopup, setShowPopup] = useState(false);
    const [selectedFile, setSelectedFile] = useState(null);
    const [preview, setPreview] = useState(false);
    const [showEditPopup, setShowEditPopup] = useState(false);
    const [showDeletePopup, setShowDeletePopup] = useState(false);
    const [fileUploads, setFileUploads] = useState<FileUpload[]>([]);
    const [snackbarOpen, setSnackbarOpen] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState("");
    const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">("success");
    const [replaceFileId, setReplaceFileId] = useState("");
    const [hoverId, setHoverId] = useState("");
    const [previewMode, setPreviewMode] = useState<FileUpload>();
    const [guideNames, setGuideNames] = useState([]);
    
    const handleSnackbarClose = () => {
        setSnackbarOpen(false);
        
    };


    const openSnackbar = () => {
        setSnackbarOpen(true);
        
        setTimeout(() => {
            setSnackbarOpen(false);
        }, 2000);
    }
  
    useEffect(() => {
        //getAllFiles();
        const unsubscribe = subscribe(setSidebarOpen);
        return () => unsubscribe();
    }, []);

    const fetchData = async () => {
        setLoading(true);
                        try {
                const response = await getAllFiles();
                            if (response) {
                        const uploads: FileUpload[] = response.map((file:any) => ({
                        ImageId: file.Id, 
                        FileName: file.Name || null,
                        Url: file.Url + "?timestamp=" + new Date().getTime()|| '',
                    }));
                    setModels(uploads);
                } else {
                }
            } catch (error) {
            } finally {
                setLoading(false);
            }
    };

    useEffect(() => {
        
        if (!showPopup) {
            fetchData();
        }
    }, [showPopup]);
    
    const openPopup = () => {
		setShowPopup(true);
	};
    const MatEdit = (params: any) => {
        const handleDeleteClick = (emailId: string) => {
            setShowDeletePopup(true);
            setemailiddelete(emailId);
        };

        const handleEditClick = (userid: string) => {
            setShowEditPopup(true);
            setUserIdEdit(userid);
        };

        return (
            <div>
                <FormControlLabel
                    control={
                        <IconButton
                            color="secondary"
                            aria-label="edit"
                            onClick={() => handleEditClick(params.userid)}
                        >
                            <EditIcon style={{ color: "blue" }} />
                        </IconButton>
                    }
                    label={""}
                />
                 <FormControlLabel
                    control={
                        <IconButton
                            color="secondary"
                            aria-label="delete"
                            onClick={() => handleDeleteClick(params.emailId)}
                        >
                            <SwapHorizIcon style={{ color: "blue" }} />
                        </IconButton>
                    }
                    label={""}
                />
                <FormControlLabel
                    control={
                        <IconButton
                            color="secondary"
                            aria-label="delete"
                            onClick={() => handleDeleteClick(params.emailId)}
                        >
                            <DeleteIcon style={{ color: "blue" }} />
                        </IconButton>
                    }
                    label={""}
                />  
            </div>
        );
    };

  // Define columns
    const columns: GridColDef[] = [
        {
            field: "ImageId",
            headerName: "ImageId",
            width: sidebarOpen ? 150 : 170,
            align: 'center',
            headerAlign: 'center',
        },
        {
            field: "FileName", 
            headerName: "Name", 
            width: sidebarOpen ? 220 : 250,
            align: 'left',
            headerAlign: 'left',
        },
        {
            field: "Url",
            headerName: "Link",
            width: sidebarOpen ? 450 : 600,
            renderCell: (params) => (
                <Link href={params.value} target="_blank" rel="noopener noreferrer" color="inherit">
                    {params.value}
                </Link>
            ),
            align: 'left',
            headerAlign: 'left',
        },
        {
            field: "actions",
            headerName: "Actions",
            sortable: false,
            width: 300,
            renderCell: (params) => (
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                   
                        <MatEdit/>
                   
                </div>
            ),
            align: 'center',
            headerAlign: 'center',
        },
    ];

// Handle edit action (replace this with your actual implementation)
const handleEdit = (row:any) => {
    // Implement your edit logic here
};
   

    const handleClose = () => {
        setShowPopup(false);
        setSelectedFile(null);
        setPreview(false);
    };

    const CustomToolbar: React.FC = () => {
        const handleExportMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
            setAnchorEl(event.currentTarget);
        };

        const handleExportMenuClose = () => {
         setAnchorEl(null);
        };
       
        const handleDownloadExcelClick = () => {
            handleExportMenuClose();
        };

        return (
            <div style={{ display: "flex", alignItems: "center" }}>
                <GridToolbarContainer>
                    <GridToolbarColumnsButton />
                    <GridToolbarFilterButton />
                    <GridToolbarDensitySelector />
                </GridToolbarContainer>
                <Button
                    aria-controls="export-menu"
                    aria-haspopup="true"
                    onClick={handleExportMenuClick}
                    style={{ marginLeft: "10px" }}
                    startIcon={<SaveAltIcon />}
                >
                    Export
                </Button>
                <Menu
                    id="export-menu"
                    anchorEl={anchorEl}
                    keepMounted
                    open={Boolean(anchorEl)}
                    onClose={handleExportMenuClose}
                >
                    <MenuItem onClick={handleDownloadExcelClick}>Download Excel</MenuItem>
                </Menu>
            </div>
        );
    };

    const handleFileUpload = (event: any) => {
      setSelectedFile(event.target.files);
    };

    const handleUpload = async () => {
      if (selectedFile) {
        try {
          setLoading(true);
          await UploadImage(
            selectedFile,
            setLoading,
            setShowPopup,
            setModels,
            setSelectedFile
          );
        } catch (error) {
            let message = translate("Upload failed. Please try again.");
          const err = error as any;

          if (err?.response?.data) {
            message =
              typeof err.response.data === "string"
                ? err.response.data
                : err.response.data.message || message;
          }
          setSnackbarMessage(message);
          setSnackbarSeverity("error");
          openSnackbar();
        } finally {
          setLoading(false);
        }
      }
    };

    const copyUrl = async (url: string) => {
        try {
            await navigator.clipboard.writeText(url);
            setSnackbarMessage(translate("URL Copied Successfully"));
            setSnackbarSeverity("success");
            openSnackbar();

            
            
            
        } catch (error) {
            console.log(error);
        }
    }



    const deleteFileIconClick = async (file: any) => {
        try {
            const status = await DeleteFile(file.ImageId);
            if (status.Success!=false) {
                setSnackbarMessage(translate("File Deleted Successfully"));
                setSnackbarSeverity("success");
                openSnackbar();
                fetchData();
            } else {
                setSnackbarMessage(status.ErrorMessage || translate("Error in Deleting File"));
                setSnackbarSeverity("error");
                openSnackbar();
            }
        } catch (error) {
            console.log(error);
            
        }
    }

    const ReplaceImage = async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      console.log(replaceFileId, "Is replace file id");
      if (file) {
        try {
          setLoading(true);
          await ReplaceFile(replaceFileId, file, setLoading);
          setModels([]);

          fetchData();
        } catch (error) {
            let message = translate("Upload failed. Please try again.");
          const err = error as any;

          if (err?.response?.data) {
            message =
              typeof err.response.data === "string"
                ? err.response.data
                : err.response.data.message || message;
          }
          setSnackbarMessage(message);
          setSnackbarSeverity("error");
          openSnackbar();
        } finally {
          setLoading(false);
        }
      }
    };


    const setReplaceFileUrl = (id : any) => {
        console.log(id, "from click");
        setReplaceFileId(id);
    }

    const previewFile = async (model : any) => {
        console.log("Preview clicked");
        
        const names = await GetGuidesUsedByFile(model.ImageId, setLoading);
        setGuideNames(names);
        setPreview(true);
        setPreviewMode(model);
    }
    const { t: translate } = useTranslation();

    return (
      
        <div className={`smooth-transition`} style={{ marginLeft: sidebarOpen ? "253px" : "23px" }}>
            <Box sx={{    maxHeight: "calc(90vh)", overflow: "auto"}}>
                <div>
                <h1>{translate('File Management')}</h1>
                    <div style={{ position: 'absolute', top: '5px', right: '2px'}}>
                    {/* <button onClick={() => openPopup} className="userButton"
                    >  
                        <AddIcon style={{ fontSize: '1.5em' }} />
                        <span style={{ position: 'relative', top: '-7px'}}> Add Field</span>
                    </button> */}
                        <Button
    onClick={() => setShowPopup(true)}
    className="userButton"
    startIcon={<AddIcon />}
    variant="contained"
>
   <span style={{ position: 'relative' }}>{translate('Add File')}</span>
</Button>
                    </div>
      
                    <Dialog open={showPopup}
                         onClose={handleClose}
                         maxWidth="md" // You can change this to 'sm', 'md', 'lg', or 'xl'
                        // fullWidth
                         PaperProps={{
                             style: {
                                 minHeight: '35vh', // Minimum height
                                 minWidth: '30vw',  // Minimum width
                             },
                         }}
                    >
                        <DialogTitle display="flex"
                                justifyContent="center"
                            alignItems="center"
                            style={{ marginLeft: "-100px" ,marginTop:"40px"}}
                        > {translate('Upload File')}</DialogTitle>
                        <DialogContent>
                        <Box
                                display="flex"
                                justifyContent="center"
                                alignItems="center"
                                minHeight="50px" // Adjust as needed
                            >
                            {/* <input type="file" onChange={handleFileChange} /> */}

                                <input type="file" onChange={handleFileUpload} accept="image/*" multiple/>
                         </Box>
                        </DialogContent>
                        <DialogActions>
                        <Button onClick={handleClose} color="primary">
                            {translate('Cancel')}
                        </Button>
                        <Button
                            onClick={async () => {
                                await handleUpload();
                                setShowPopup(false);
                            }}
                            color="primary"
                            //disabled={!selectedFile}
                        >
                            {translate('Upload')}
                        </Button>
                        </DialogActions>
                    </Dialog>
                </div>
                {loading ? (
                     <div className="Loaderstyles">
                        <img
      src={loader}
			alt="Spinner"
			className="LoaderSpinnerStyles"
      />
                    </div>
            ) : (
                    <>
                    <Box  sx={{display:"flex",flexWrap:"wrap",}}>
                    {
                        models.map((model,index) => {
                            return (
                                <Card key={index} sx={{
                                    display: "flex", flexDirection: "column", height: "223px",
                                    position: "relative", margin: "10px", width: "277px", borderRadius: "15px",
                                    "&:hover": {
                                        transform: "scale(1.05)", // Slight pop-up effect
                                        boxShadow: 10, // Stronger shadow on hover
                                    }
                                }}>
                                    <Box sx={{
                                        position: "relative",
                                        
                                        display: "flex", justifyContent: "center", alignItems: "center", flexGrow: 1,cursor: "pointer"
                                        
                                    }}
                                    onMouseEnter={() => setHoverId(model.ImageId)}
                                    onMouseLeave={() => setHoverId("")}>
                                    <CardMedia
                                        component="img"
                                        image={model.Url||""}
                                            alt={model.FileName || "Image"}
                                            
                                            sx={{
                                                height: "162px",

                                                width: "261px",
                                                objectFit: "cover",
                                                filter: hoverId == model.ImageId ? "blur(1px)" : "none",
                                                borderRadius: 2,
                                                margin: 1
                                            }}
                                            onClick={()=>previewFile(model)}
                                        className="image-hover"
                                        />
                                        
                                        
                                        { hoverId == model.ImageId &&(
                                            <Box sx={{
                                                position: "absolute",
                                                display: "flex",
                                                justifyContent: "center",
                                                alignItems: "center",
                                                
                                            }}
                                            onClick={()=>previewFile(model)}
                                            >
                                                {/* <Preview /> */}
                                                <img src={PreviewImage} alt="Preview Image" />
                                                {/* <Typography>Preview</Typography> */}
                                            </Box>)
                                        }
                                        
                                            
                                            
                                    
                                    <IconButton
                                        onClick={() => deleteFileIconClick(model)}
                                        style={{ position: "absolute", top: 5, right: 5, color: "red" }}
                                    >
                                        <img src={DeleteRed} alt="Delete"/>
                                    </IconButton>
                                    </Box>
                                    <CardActions style={{ display: "flex", justifyContent: "space-between", padding: "0px 9px" }}>
                                        <Typography variant="body2" noWrap style={{ maxWidth: "50%" }}>{model.FileName}</Typography>
                                        <Box>
                                    <IconButton onClick={() => copyUrl(model.Url)}>
                                    <img src={copy} alt="copy"/>
                                        </IconButton>
                                        <Tooltip title="Replace Image" arrow>
                                        <IconButton 
                                                onClick={(event) => {
                                                    setReplaceFileUrl(model.ImageId);
                                                event?.stopPropagation();
                                                document.getElementById("file-upload")?.click();
                                            }}>
                                                    <img src={Replace} alt="Replace"/>
                                                    
                                                </IconButton>
                                                
                                            </Tooltip>
                                            <input
                                                type="file"
                                                id="file-upload"
                                                style={{ display: "none" }}
                                                accept="image/*"
                                                onChange={ReplaceImage}
                                            />
                                            </Box>
                                    </CardActions>
                                </Card>
                                );
                        })
                            }
                            </Box>
                            {
                                preview && (
                                    <Modal
                                        open={preview}
                                        onClose={() => {
                                            setPreview(false);
                                            setPreviewMode(undefined);
                                        }
                                            
                                        }
                                        aria-labelledby="modal-modal-title"
                                        aria-describedby="modal-modal-description"
                                        >
                                        <Box sx={{
                                            position: "absolute",
                                            top: "50%",
                                            left: "50%",
                                            transform: "translate(-50%, -50%)",
                                            bgcolor: "background.paper",
                                            boxShadow: 24,

                                            maxWidth: "600px",
                                            maxHeight: "600px",
                                            p: 4,
                                        }}>
                                            
                                            <img src={previewMode?.Url|| ""} alt="Model Image" style={{maxWidth:"500px",maxHeight:"500px"}}/>
                                            <Typography id="modal-modal-description" sx={{ mt: 2 }}>
                                                Name: {previewMode?.FileName}
                                            </Typography>
                                            <Typography id="modal-modal-description" sx={{ mt: 2 }}>
                                                {translate('Used Guides :')} {guideNames?.length<1 ? translate('No Guides Used this file ') : guideNames }
                                            </Typography>

                                        </Box>
                                    </Modal>
                                )
                                
                    }
                    <Snackbar
                        open={snackbarOpen}
                        autoHideDuration={6000}
                        onClose={handleSnackbarClose}
                        anchorOrigin={{ vertical: "top", horizontal: "center" }}
                        sx={{ zIndex: 10000,marginTop:4}}
                    >
                        <Alert
                            onClose={handleSnackbarClose}
                            severity={snackbarSeverity}
                            sx={{ width: "100%" }}
                        >
                            {translate(snackbarMessage)}
                        </Alert>
                    </Snackbar>
                    {/* <CustomGrid
                        rows={models}
                        columns={columns}
                        pageSize={100}
                        totalRows={models.length}   
                        onPageChange={(newPage) => newPage}
                        onPageSizeChange={(newPageSize) => newPageSize}
                        rowsPerPageOptions={[10, 20, 50, 100]}
                        Toolbar={CustomToolbar}
                        /> */}
                        </>
            )}
            </Box>
            </div>
    );
};

export default FileList;
