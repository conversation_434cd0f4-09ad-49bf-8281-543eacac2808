import React, { useState } from 'react';
import { <PERSON>alog, DialogA<PERSON>, DialogContent, DialogTitle, TextField, Button, IconButton, Typography } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { CheckGuideNameExists, CopyGuide } from "../../services/GuideService";
import { useSnackbar } from "../../SnackbarContext";
import { useTranslation } from 'react-i18next';

interface Announcement {
    AccountId: string;
    Content: string;
    CreatedBy: string;
    CreatedDate: string;
    Frequency: string;
    GuideId: string;
    GuideStatus: string;
    GuideType: string;
    Name: string;
    OrganizationId: string;
    Segment: string;
    TargetUrl: string;
    TemplateId: string;
    UpdatedBy: string;
    UpdatedDate: string;
}

interface CloneInteractionDialogProps {
    open: boolean;
    handleClose: () => void;
    initialName: Announcement; 
    onCloneSuccess: () => void;
    name: string;
}

const CloneInteractionDialog: React.FC<CloneInteractionDialogProps> = ({ open, handleClose, initialName, onCloneSuccess,name }) => {
    const { t: translate } = useTranslation();
    const [announcementName, setAnnouncementName] = useState(`${initialName.Name}-Copy`);
    const [error, setError] = useState<string | null>(null);
    const { openSnackbar } = useSnackbar();

    const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;
        setAnnouncementName(newValue);

        // Validate the length of the new value immediately while typing
        if (newValue.trim() === "") {
            setError(translate("Name is required."));
        } else if (newValue.trim().length < 3) {
            setError(translate("Guide Name must be at least 3 characters."));        
        }
        else if (newValue.trim().length > 50) {
            setError(translate("Guide Name should not exceed 50 characters."));
        }
        else {
            setError(null); // Clear the error if the input is valid
        }
    };

    
    const handleClone = async () => {
        // Final validation before cloning
        if (error) {
            return; // Exit if there's an error
        }
        
        const guideName = announcementName; 
        const accountId = initialName.AccountId;
        const organizationId = initialName.OrganizationId;

        const existsResponse = await CheckGuideNameExists(guideName, accountId, name);
        
        if (existsResponse === true) {
            setError(translate("A guide with this name already exists."));
        } else {
            const guideId = initialName.GuideId; 
            const guideStatus = initialName.GuideStatus; 
            const guideType = initialName.GuideType;
            const copyResponse = await CopyGuide(guideId, organizationId, announcementName, accountId, guideType);
            if (copyResponse.Success) {
                openSnackbar(`${announcementName} ${guideType} ${translate('Copied Successfully. Please Check The Drafts')}`, "success");
                onCloneSuccess();
                handleClose();
            } else {
                openSnackbar(translate(copyResponse.ErrorMessage), "error");
            }
        }
    };
    const paraGraph = `${translate('Clone')} ${initialName.Name}`
    return (
        <Dialog open={open} onClose={handleClose} className='qadpt-webclonepopup'>
            <div className="qadpt-title-sec">
                <DialogTitle className='qadpt-title'>{paraGraph}</DialogTitle>
                <IconButton 
                    edge="end" 
                    color="inherit" 
                    onClick={handleClose} 
                    aria-label="close" 
                    className='qadpt-close'
                >
                    <CloseIcon />
                </IconButton>
            </div>
            <DialogContent>
                <TextField
                    style={{marginTop:10}}
                    fullWidth
                    className="clone-fld"
                    variant="outlined"
                    label={translate("Name")}
                    value={announcementName}
                    onChange={handleNameChange}
                    helperText={error}
                    error={!!error}
                    inputProps={{ maxLength: 50 }}
                />
            </DialogContent>
            <DialogActions>
                <Button onClick={handleClone} variant="contained" color="primary">
                    {translate("Clone")}
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default CloneInteractionDialog;
