// components/NavigationPrompt.tsx
import React from "react";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import Button from "@mui/material/Button";
import { usePrompt } from "./usePrompt";

export default function NavigationPrompt({ when }: { when: boolean }) {
    const { showPrompt, confirmNavigation, cancelNavigation } = usePrompt(when);

    return (
        <Dialog open={showPrompt}>
            <DialogTitle>Unsaved Changes</DialogTitle>
            <DialogContent>
                You have unsaved changes. Are you sure you want to leave?
            </DialogContent>
            <DialogActions>
                <Button onClick={confirmNavigation} color="primary">
                    Leave Anyway
                </Button>
                <Button onClick={cancelNavigation} color="secondary" autoFocus>
                    Stay
                </Button>
            </DialogActions>
        </Dialog>
    );
}
