{"name": "quickadapt", "version": "0.1.0", "private": true, "dependencies": {"@date-io/dayjs": "^3.0.0", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@handsontable/react": "^14.4.0", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^5.15.19", "@mui/joy": "^5.0.0-beta.48", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.19", "@mui/styled-engine-sc": "^5.11.11", "@mui/system": "^5.15.19", "@mui/x-data-grid": "^7.7.0", "@mui/x-date-pickers": "^7.23.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.0.0", "@types/jest": "^29.5.3", "@types/node": "^18.18.3", "@types/react": "^18.2.22", "@types/react-dom": "^18.2.7", "ajv": "^8.17.1", "antd": "^5.8.6", "axios": "^1.7.2", "chart.js": "^4.4.3", "date-fns": "^2.30.0", "date-fns-tz": "^3.1.3", "detect-port-alt": "1.1.6", "env-cmd": "^10.1.0", "handsontable": "^14.4.0", "i18next": "^23.2.3", "i18next-browser-languagedetector": "^6.1.5", "i18next-http-backend": "^2.5.2", "jsencrypt": "^3.3.2", "jss": "^10.10.0", "jss-rtl": "^0.3.0", "jwt-decode": "^3.1.2", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mui-phone-number": "^3.0.3", "oidc-client": "^1.10.1", "oidc-client-ts": "^3.0.1", "primeicons": "^7.0.0", "primereact": "^10.7.3", "react": "^18.3.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.3.0", "react-i18next": "^14.1.2", "react-icons": "^5.2.1", "react-perfect-scrollbar": "^1.5.8", "react-router-dom": "^6.23.1", "react-scripts": "5.0.1", "react-transition-group": "^4.4.5", "sass": "^1.77.8", "stylis": "^4.3.2", "stylis-plugin-rtl": "^2.1.1", "typescript": "^5.2.2", "uuid": "^10.0.0", "web-vitals": "^3.2.0"}, "scripts": {"start": "env-cmd -f .env.development react-scripts start", "start:dev-integration": "env-cmd -f .env.development.integration react-scripts start", "start:qa-integration": "env-cmd -f .env.qa.integration react-scripts start", "start:production": "env-cmd -f .env.production react-scripts start", "start:staging": "env-cmd -f .env.staging react-scripts start", "build:dev": "env-cmd -f .env.dev react-scripts build", "build:pref-testing": "env-cmd -f .env.performancetesting react-scripts build", "build:staging": "env-cmd -f .env.staging react-scripts build", "build:cloud": "env-cmd -f .env.cloud react-scripts build", "build:production": "env-cmd -f .env.prod react-scripts build", "build:qa": "env-cmd -f .env.qa react-scripts build", "build:uat": "env-cmd -f .env.uat react-scripts build", "build:development": "env-cmd -f .env.development react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/axios": "^0.14.0", "@types/google__maps": "^0.5.20", "@types/js-cookie": "^3.0.6", "@types/node-sass": "^4.11.7", "@types/react": "^18.2.22", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.2.7", "@types/uuid": "^10.0.0", "history": "^5.3.0"}, "resolutions": {"react": "^18.3.0", "react-dom": "^18.3.0"}}