import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Container,
  Box,
  IconButton,
  Tooltip,
  Button,
  CircularProgress,
  Snackbar,
  Alert,
  Typography,
  Paper,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import SaveIcon from "@mui/icons-material/Save";
import { useTranslation } from "react-i18next";
import { isSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import { getScriptByAgentId } from "../../services/ScriptService";
import { UpdateSystemPrompt, SystemPromptData } from "../../services/SystemPromtServices";
import { useAuth } from "../auth/AuthProvider";

const Scripts = () => {
  const { t: translate } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { userDetails } = useAuth();
  const agentData = location.state?.agentData;
  const [, setSidebarOpen] = useState(isSidebarOpen()); // Only need the setter for the subscription
  const [scriptContent, setScriptContent] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error" | "info" | "warning">("info");
  const [updatedSystemPrompt, setUpdatedSystemPrompt] = useState<SystemPromptData | null>(null);
  const [botNameValue, setBotNameValue] = useState("");
  const [hasChanges, setHasChanges] = useState(false);
  const [originalScriptContent, setOriginalScriptContent] = useState("");
  const [originalBotName, setOriginalBotName] = useState("");
  const [botNameError, setBotNameError] = useState("");
  const [showUnsavedChangesDialog, setShowUnsavedChangesDialog] = useState(false);

  // Validation function for bot name
  const validateBotName = (name: string): string => {
    const trimmedName = name.trim();

    // Check for invalid characters (only letters and spaces allowed)
    const validCharPattern = /^[a-zA-Z\s]*$/;
    if (!validCharPattern.test(name)) {
      return translate("Bot name can only contain letters and spaces");
    }

    if (trimmedName.length < 3) {
      return translate("Bot name must be at least 3 characters long");
    }
    if (trimmedName.length > 50) {
      return translate("Bot name must not exceed 50 characters");
    }
    return "";
  };

  useEffect(() => {
    const unsubscribe = subscribe(setSidebarOpen);
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (agentData) {
      loadSystemPrompt();
      setBotNameValue(agentData.BotName || "");
    }
  }, [agentData]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadSystemPrompt = () => {
    if (!agentData) return;

    try {
      setIsLoading(true);
      // Use the SystemPrompt directly from agentData
      if (agentData.SystemPrompt) {
        // Ensure the SystemPrompt is a string
        const promptContent = typeof agentData.SystemPrompt === 'string'
          ? agentData.SystemPrompt
          : JSON.stringify(agentData.SystemPrompt);

        setScriptContent(promptContent);
        setOriginalScriptContent(promptContent);
        setBotNameValue(agentData.BotName || "");
        setOriginalBotName(agentData.BotName || "");
        setHasChanges(false);

        // Create a complete system prompt object
        const systemPromptData: SystemPromptData = {
          Id: agentData.Id || '',
          OrganizationId: agentData.OrganizationId || userDetails?.OrganizationId || '',
          AccountId: agentData.AccountId || '',
          BotName: agentData.BotName || '',
          SystemPrompt: promptContent,
          CreatedDate: agentData.CreatedDate,
          CreatedBy: agentData.CreatedBy,
          UpdatedDate: agentData.UpdatedDate,
          UpdatedBy: agentData.UpdatedBy
        };

        setUpdatedSystemPrompt(systemPromptData);
      } else {
        // If SystemPrompt is not available, try to fetch it
        fetchScriptData();
      }
    } catch (error) {
      console.error("Error loading system prompt:", error);
      setSnackbarMessage(translate("Failed to load system prompt"));
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchScriptData = async () => {
    if (!agentData) return;

    try {
      setIsLoading(true);
      const scriptData = await getScriptByAgentId(agentData.Id || agentData.AccountId);

      // Ensure the content is a string
      const promptContent = typeof scriptData.content === 'string'
        ? scriptData.content
        : JSON.stringify(scriptData.content);

      setScriptContent(promptContent);
      setOriginalScriptContent(promptContent);
      setBotNameValue(agentData.BotName || "");
      setOriginalBotName(agentData.BotName || "");
      setHasChanges(false);

      // Create a complete system prompt object
      const systemPromptData: SystemPromptData = {
        Id: agentData.Id || '',
        OrganizationId: agentData.OrganizationId || userDetails?.OrganizationId || '',
        AccountId: agentData.AccountId || '',
        BotName: agentData.BotName || '',
        SystemPrompt: promptContent,
        CreatedDate: agentData.CreatedDate,
        CreatedBy: agentData.CreatedBy,
        UpdatedDate: agentData.UpdatedDate,
        UpdatedBy: agentData.UpdatedBy
      };

      setUpdatedSystemPrompt(systemPromptData);
    } catch (error) {
      console.error("Error fetching script data:", error);
      setSnackbarMessage(translate("Failed to load script data"));
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackClick = () => {
    if (hasChanges) {
      // Show confirmation dialog if there are unsaved changes
      setShowUnsavedChangesDialog(true);
    } else {
      // Navigate directly if no changes
      navigate("/settings/agents");
    }
  };

  const handleSaveClick = async () => {
    if (!agentData || !updatedSystemPrompt) return;

    // Validate bot name before saving
    const nameValidationError = validateBotName(botNameValue);
    if (nameValidationError) {
      setBotNameError(nameValidationError);
      setSnackbarMessage(nameValidationError);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
      return;
    }

    try {
      setIsSaving(true);

      // Ensure the script content is a string
      const promptContent = typeof scriptContent === 'string'
        ? scriptContent
        : scriptContent;

      // Update the system prompt object with the current user as the updater
      const currentDate = new Date().toISOString();
      const systemPromptToUpdate: SystemPromptData = {
        ...updatedSystemPrompt,
        SystemPrompt: promptContent,
        BotName: botNameValue.trim(), // Include bot name in the update
        UpdatedDate: currentDate,
        UpdatedBy: userDetails?.EmailId || userDetails?.UserName || 'current-user'
      };

      console.log("Sending system prompt update:", systemPromptToUpdate);

      // Call the API to update the system prompt
      const result = await UpdateSystemPrompt(systemPromptToUpdate);

      if (result.success) {
        // Update the local agentData with the new SystemPrompt and BotName
        if (agentData.SystemPrompt) {
          agentData.SystemPrompt = promptContent;
        }
        agentData.BotName = botNameValue.trim();

        // Update the local state with the updated system prompt
        setUpdatedSystemPrompt(systemPromptToUpdate);

        // Update original values to reset change tracking
        setOriginalScriptContent(promptContent);
        setOriginalBotName(botNameValue.trim());
        setHasChanges(false);

        setSnackbarMessage(translate("System prompt saved successfully"));
        setSnackbarSeverity("success");
        setSnackbarOpen(true);
      } else {
        setSnackbarMessage(translate("Failed to save system prompt: ") + (result.message || ''));
        setSnackbarSeverity("error");
        setSnackbarOpen(true);
      }
    } catch (error) {
      console.error("Error saving system prompt:", error);
      setSnackbarMessage(translate("An error occurred while saving the system prompt"));
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsSaving(false);
    }
  };

  const handleScriptChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = event.target.value;
    setScriptContent(value);

    // Update the system prompt object with the new content
    if (updatedSystemPrompt) {
      setUpdatedSystemPrompt({
        ...updatedSystemPrompt,
        SystemPrompt: value
      });
    }

    // Check if content has changed from original
    const hasScriptChanged = value !== originalScriptContent;
    const hasBotNameChanged = botNameValue !== originalBotName;
    setHasChanges(hasScriptChanged || hasBotNameChanged);
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const handleBotNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    let value = event.target.value;

    // Limit to 50 characters
    if (value.length > 50) {
      value = value.substring(0, 50);
    }

    // Only allow letters and spaces
    const validCharPattern = /^[a-zA-Z\s]*$/;
    if (!validCharPattern.test(value)) {
      // If invalid characters are present, don't update the value
      return;
    }

    setBotNameValue(value);

    // Validate bot name in real-time
    const validationError = validateBotName(value);
    setBotNameError(validationError);

    // Check if bot name has changed from original
    const hasScriptChanged = scriptContent !== originalScriptContent;
    const hasBotNameChanged = value !== originalBotName;
    setHasChanges(hasScriptChanged || hasBotNameChanged);
  };

  // Dialog action handlers
  const handleSaveAndLeave = async () => {
    setShowUnsavedChangesDialog(false);

    if (!agentData || !updatedSystemPrompt) return;

    // Validate bot name before saving
    const nameValidationError = validateBotName(botNameValue);
    if (nameValidationError) {
      setBotNameError(nameValidationError);
      setSnackbarMessage(nameValidationError);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
      return;
    }

    try {
      setIsSaving(true);

      // Ensure the script content is a string
      const promptContent = typeof scriptContent === 'string'
        ? scriptContent
        : scriptContent;

      // Update the system prompt object with the current user as the updater
      const currentDate = new Date().toISOString();
      const systemPromptToUpdate: SystemPromptData = {
        ...updatedSystemPrompt,
        SystemPrompt: promptContent,
        BotName: botNameValue.trim(),
        UpdatedDate: currentDate,
        UpdatedBy: userDetails?.EmailId || userDetails?.UserName || 'current-user'
      };

      // Call the API to update the system prompt
      const result = await UpdateSystemPrompt(systemPromptToUpdate);

      if (result.success) {
        // Update the local agentData with the new SystemPrompt and BotName
        if (agentData.SystemPrompt) {
          agentData.SystemPrompt = promptContent;
        }
        agentData.BotName = botNameValue.trim();

        // Update the local state with the updated system prompt
        setUpdatedSystemPrompt(systemPromptToUpdate);

        // Update original values to reset change tracking
        setOriginalScriptContent(promptContent);
        setOriginalBotName(botNameValue.trim());
        setHasChanges(false);

        // Navigate back after successful save
        navigate("/settings/agents");
      } else {
        setSnackbarMessage(translate("Failed to save system prompt: ") + (result.message || ''));
        setSnackbarSeverity("error");
        setSnackbarOpen(true);
      }
    } catch (error) {
      console.error("Error saving system prompt:", error);
      setSnackbarMessage(translate("An error occurred while saving the system prompt"));
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDiscardAndLeave = () => {
    setShowUnsavedChangesDialog(false);
    navigate("/settings/agents");
  };

  const handleCancelLeave = () => {
    setShowUnsavedChangesDialog(false);
  };



  return (
    <Container maxWidth={false} sx={{ px: 3 }}>
      <div className="qadpt-midpart setng-box">
        <div className="qadpt-content-block">
          <div>
            <div className="qadpt-head">
              <div className="qadpt-title-sec">
                {agentData && (
                  <IconButton
                    onClick={handleBackClick}
                    sx={{ right:"10px", gap:"10px"}}
                  >
                    <Tooltip title={translate("Back to System Prompts")} arrow>
                      <ArrowBackIcon />

                    </Tooltip>
                    <div className="qadpt-title" style={{color:"black"}}>{translate("System Prompt Editor")}</div>
                  </IconButton>

                )}
                <div className="qadpt-description" style={{marginLeft: "35px"}}>
                  {agentData ? `${translate("Editing prompt for")} ${agentData.BotName}` : translate("View and manage system prompts")}
                </div>
              </div>

            </div>

            <div style={{ position: 'relative',bottom:"50px" }}>
              <Box className="qadpt-content-box">
                {agentData ? (
                  isLoading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                      <CircularProgress />
                    </Box>
                  ) : (
                    <div>
                      <Box sx={{ mb: 2, display: 'flex', bottom: "20px", flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Box>
                          {/* <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 0.5 }}>
                            {translate("Agent Details")}
                          </Typography> */}
                          <Box sx={{ display: 'flex', gap: 3 }}>
                            <Typography  sx={{ display: 'flex', alignItems: 'center' ,whiteSpace:"nowrap",bottom:"22px",position:"relative"}}>
                              <strong style={{bottom:"10px",position:"relative"}}>{translate("Bot Name")}:</strong>
                              <Box sx={{ display: 'flex', alignItems: 'center', ml: 1 }}>
                                <TextField
                                  size="small"
                                  value={botNameValue}
                                  onChange={handleBotNameChange}
                                  variant="outlined"
                                  error={!!botNameError}
                                  helperText={botNameError || `${botNameValue.trim().length}/50 characters`}
                                  inputProps={{
                                    maxLength: 50,
                                    pattern: "[a-zA-Z\\s]*",
                                    title: "Only letters and spaces are allowed"
                                  }}
                                  sx={{ width: '200px' }}
                                />
                              </Box>
                            </Typography>
                            <Typography variant="body2">
                              <strong>{translate("Created By")}:</strong> {agentData.CreatedBy || "N/A"}
                            </Typography>
                            <Typography variant="body2">
                              <strong>{translate("Created")}:</strong> {agentData.CreatedDate ? new Date(agentData.CreatedDate).toLocaleDateString() : "N/A"}
                            </Typography>
                            <Typography variant="body2">
                              <strong>{translate("Updated")}:</strong> {agentData.UpdatedDate ? new Date(agentData.UpdatedDate).toLocaleDateString() : "N/A"}
                            </Typography>
                          </Box>
                          </Box>
                          {agentData && (
                <div className="qadpt-right-part">
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={isSaving ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
                    onClick={handleSaveClick}
                    disabled={!hasChanges|| isSaving || !!botNameError}
                    size="large"
                    sx={{ px: 3,lineHeight: 0.5 ,borderRadius: "12px",bottom:"35px"}}
                  >
                    {hasChanges ? translate("Update") : translate("Save")}
                  </Button>
                </div>
              )}
                        </Box>


                      <Paper
                        elevation={3}
                        sx={{
                          p: 2,
                          backgroundColor: '#f8f9fa',
                          border: '1px solid #e0e0e0',
                          borderRadius: '8px',
                          height: '55vh',
                          bottom: '110px',
                          position: 'relative',
                          width: '100%',
                          overflow: 'hidden',
                          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
                          '&:hover': {
                            boxShadow: '0 6px 10px rgba(0, 0, 0, 0.08)'
                          }
                        }}
                      >
                        <textarea
                          value={scriptContent}
                          onChange={handleScriptChange}
                          placeholder={translate("Enter your system prompt here...")}
                          style={{
                            width: '100%',
                            height: '100%',
                            padding: '16px',
                            fontFamily: 'Consolas, "Courier New", monospace',
                            fontSize: '14px',
                            lineHeight: '1.6',
                            backgroundColor: '#ffffff',
                            border: 'none',
                            borderRadius: '4px',
                            resize: 'none',
                            outline: 'auto',
                            color: '#333',
                            boxShadow: 'inset 0 1px 2px rgba(0, 0, 0, 0.05)',
                            transition: 'all 0.2s ease-in-out'
                          }}
                        />
                      </Paper>
                    </div>
                  )
                ) : (
                  <div>
                    <p>{translate("Select a system prompt from the System Prompts page to view and edit it.")}</p>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => navigate("/settings/agents")}
                      sx={{ mt: 2 }}
                    >
                      {translate("Go to System Prompts")}
                    </Button>
                  </div>
                )}
              </Box>
            </div>
          </div>
        </div>
      </div>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {translate(snackbarMessage)}
        </Alert>
      </Snackbar>

      {/* Unsaved Changes Confirmation Dialog */}
      <Dialog
        open={showUnsavedChangesDialog}
        onClose={handleCancelLeave}
        aria-labelledby="unsaved-changes-dialog-title"
        aria-describedby="unsaved-changes-dialog-description"
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle id="unsaved-changes-dialog-title">
          {translate("Unsaved Changes")}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="unsaved-changes-dialog-description">
            {translate("You have unsaved changes. Do you want to save before leaving?")}
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ p: 2, gap: 1 }}>
          <Button
            onClick={handleSaveAndLeave}
            variant="contained"
            color="primary"
            disabled={isSaving || !!botNameError}
            startIcon={isSaving ? <CircularProgress size={16} color="inherit" /> : <SaveIcon />}
          >
            {translate("Save")}
          </Button>
          <Button
            onClick={handleDiscardAndLeave}
            variant="outlined"
            color="inherit"
            sx={{
              borderColor: '#666',
              color: '#666',
              '&:hover': {
                borderColor: '#333',
                backgroundColor: '#f5f5f5'
              }
            }}
          >
            {translate("Don't Save")}
          </Button>
          <Button
            onClick={handleCancelLeave}
            variant="outlined"
            color="error"
            sx={{
              borderColor: '#d32f2f',
              color: '#d32f2f',
              '&:hover': {
                borderColor: '#b71c1c',
                backgroundColor: '#ffebee'
              }
            }}
          >
            {translate("Cancel")}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default Scripts;
