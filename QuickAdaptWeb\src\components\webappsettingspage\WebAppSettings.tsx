import React, { useState, useEffect, ChangeEvent, useContext } from 'react';
import {
    Con<PERSON><PERSON>, <PERSON>po<PERSON>, Button, Grid, Switch, TextField, Select, MenuItem, FormControl, InputLabel, Card, CardContent, Divider, FormControlLabel, IconButton, Box, InputAdornment, Tooltip, RadioGroup
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import UnPublishIcon from '../../assets/icons/UnPublishIcon.svg';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import EditIcon from '@mui/icons-material/Edit';
import OpeninNewWindow from '../../assets/icons/OpenNewWindow.svg';
import PublishIcon from '../../assets/icons/PublishIcon.svg';
import checksmall from '../../assets/icons/check_small.svg';
import Saveicon from '../../assets/icons/SaveIcon.svg';
import Warning from '../../assets/icons/Warning.svg';
import ShareIcon from '../../assets/icons/ShareIcon.svg';
import Targetdelete from '../../assets/icons/Targetdelete.svg';
import DrawOutlinedIcon from '@mui/icons-material/DrawOutlined';
import Radio, { RadioProps } from '@mui/material/Radio';
import { styled } from '@mui/material/styles';
import { SavePageTargets } from '../../models/SavePageTarget';
import { UpdateGuidName, PublishGuide, UnPublishGuide, SubmitUpdateGuid, SavePageTarget, DeletePageTarget, UpdatePageTarget, GetPageTargets, GetGudeDetailsByGuideId } from '../../services/GuideService';
import { useSnackbar } from '../../SnackbarContext';
import DeleteIcon from '@mui/icons-material/Delete';
import AddOutlinedIcon from '@mui/icons-material/AddOutlined';
import ConfirmationDialog from './ConfirmationDialog';
import { cloudoff, Delete } from '../../assets/icons/icons';
import { getAllGuides } from '../../services/ProfileSettingPageService';
import { openGuideInBuilder } from '../../utils/openGuideInBuilder';
import { useExtension } from '../../ExtensionContext';
import ExtensionRequiredPopup from '../common/ExtensionRequiredPopup';
import { AccountContext } from '../account/AccountContext';

interface Trigger {
    pageRule: string;
    url: string;
    logicalOperator?: string;
    PageTargetId: string;
}
interface TextFieldProperties {
    Id: string; 
    Text: string;
    Alignment: string; 
    Hyperlink: string;
    Emoji: string;
    TextProperties: string; 
  }
interface GuideStep {
    TextFieldProperties: TextFieldProperties[]; 
   // ImageProperties: ImageProperties[]; // Array of ImageProperties
    setting: string;
    VideoEmbedCode: string; 
    StepId: string; 
    Overlay: boolean;
    StepTitle: string;
    Arrow: boolean; 
    ElementPath: string; 
    Classes: string; 
    IsClickable: boolean;
    AutoPosition: boolean; 
    Position: string; 
    HtmlSnippet: string; 
    Modal: string;
    Canvas: string; 
    Design: string; 
    Hotspot: string; 
    Tooltip: string; 
    Advanced: string; 
    Animation: string; 
    //ButtonSection: ButtonSection[]; 
    //LayoutPositions: SectionLayout[]; 
}

  
  
interface GuideDetails {
    GuideId: string;
    GuideType: string;
    Description: string; 
    Name: string;
    Content: string; 
    OrganizationId: string;
    CreatedDate: string | null; 
    UpdatedDate: string | null; 
    CreatedBy: string;
    UpdatedBy: string;
    TargetUrl: string;
    Frequency: string;
    TemplateId: string; 
    Segment: string; 
    AccountId: string;
    GuideStatus: string;
    PublishType: string;
    UnPublishType: string; 
    PublishDate: string | null; 
    UnPublishDate: string | null; 
    Visited: boolean; 
    VisitedDate: string | null; 
    AutoTrigger: boolean;
    GuideStep: GuideStep[];
  }
  
  interface PageTarget {
    PageTargetId: string; 
    GuideId: string; 
    OrganizationId: string; 
    Condition: string; 
    Operator: string; 
    Value: string;
    CreatedBy: string; 
    CreatedDate: string; 
    UpdatedBy: string; 
    UpdatedDate: string; 
}
  
  interface Guide {
      GuideDetails: GuideDetails;
      PageTargets: PageTarget[];   
  }
const WebappSettings: React.FC = () => {
    const { t: translate } = useTranslation();
    const currentDateTime = new Date().toISOString().slice(0, 16);
    const currentDate = new Date();
    // const utcDate = new Date(currentDate);

    // Add 2 hours to the current date and time

    // Format both the current date and the unpublish date
    //const currentdatTime = utcDate.toLocaleString();
    // utcDate.setHours(utcDate.getHours() + 2);
   
    const [hasChanges, setHasChanges] = useState(false);
    const [guide, setGuide] = useState<Guide | null>(null);
    const [pageTargetsSaved, setPageTargetsSaved] = useState(false);  // Track if page targets are saved
    const [newTriggersAdded, setNewTriggersAdded] = useState(false);  // Track if new page targets are added
    const location = useLocation();
    const navigate = useNavigate();
    const { openSnackbar } = useSnackbar();
    const [guideName, setGuideName] = useState(guide?.GuideDetails?.Name || " ");
    const { guideId } = useParams<{ guideId: string }>();
    const [currentGuideId, setGuideId] = useState(guideId || "");
    const [organizationId, setOrganizationId] = useState(guide?.GuideDetails.OrganizationId || "");
    const [accountId, setAccountId] = useState(guide?.GuideDetails?.AccountId || "");
    const [guideStatus, setGuideStatus] = useState(guide?.GuideDetails?.GuideStatus || "");
    const [isEditing, setIsEditing] = useState(false);
    const [selectedFrequency, setSelectedFrequency] = useState<string>(guide?.GuideDetails?.Frequency || 'onceInSession');
    const [guideType, setguideType] = useState(guide?.GuideDetails?.GuideType || "");
    const [updatedBy, setUpdatedBy] = useState(guide?.GuideDetails?.UpdatedBy);
    const [createdBy, setCreatedBy] = useState(guide?.GuideDetails?.CreatedBy);
    const [isAutoTriggerEnabled, setIsAutoTriggerEnabled] = useState(guide?.GuideDetails?.AutoTrigger);
    const [publishOption, setPublishOption] = useState(guide?.GuideDetails?.PublishType || "immediately");
    const [unpublishOption, setUnpublishOption] = useState(guide?.GuideDetails?.UnPublishType || "Manually");
    const [Descriptionvalue, setDescriptionValue] = useState(guide?.GuideDetails?.Description);
    const [targetUrl, setTargetUrl] = useState(guide?.GuideDetails?.TargetUrl || "");
    const [CreatedDate, setCreatedDate] = useState(guide?.GuideDetails?.CreatedDate);
    const [PublishDate, setPublishDate] = useState(guide?.GuideDetails?.PublishDate || currentDate);
    const [UnPublishDate, setUnPublishDate] = useState(guide?.GuideDetails?.UnPublishDate || currentDate);
    const [frequencyDropdown, setfrequencyDropdown] = useState('');
    const [isPublished, setIsPublished] = useState(false);
    const [isUnPublished, setIsUnPublished] = useState(false);
    const [dialogOpen, setDialogOpen] = useState(false);
    const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
    const [triggers, setTriggers] = useState<Trigger[]>([{ pageRule: "Equals", url: guide?.GuideDetails?.TargetUrl || '', logicalOperator: "", PageTargetId: "" }]);
    const [errorMessage, setErrorMessage] = useState<string[]>([]);
    const [open, setOpen] = useState(false); // Controls the popup visibility
    const [initialGuide, setInitialGuide] = useState({
        GuideId: '',
        GuideType: '',
        Name: '',
        OrganizationId: '',
        CreatedBy: '',
        UpdatedBy: '',
        Frequency: '',
        AccountId: '',
        GuideStatus: '',
        AutoTrigger: false,
        Publish: false,
        UnPublish: false,
        Description: '',
        TargetUrl: '',
    })

    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false); // To track unsaved changes
    const [customPublishDate, setCustomPublishDate] = useState("2024-09-26T07:00");
    const [customUnPublishDate, setCustomUnPublishDate] = useState("2024-09-26T07:00");
    const { roles } = useContext(AccountContext);
    
   
    
    const handleCustomDateChange = (event: any) => {
        setPublishDate(event.target.value); // Update state with new date value
    };

    const handleCustomDateChangeTwo = (event: any) => {
        setUnPublishDate(event.target.value); // Update state with new date value
    };
    const [guidestep, setGuidestep] = useState(
        Array.isArray(guide?.GuideDetails.GuideStep)
            ? guide?.GuideDetails.GuideStep
            : []
    );

    const CustomDivider = styled('div')(({ theme }) => ({
        display: 'flex',
        alignItems: 'center',
        textAlign: 'center',
        // margin: '8px 0',
        '&:before, &:after': {
            content: '""',
            //flex: 1,
            //borderBottom: `1px solid ${theme.palette.divider}`,
            margin: '0 8px',
        },
    }));
    useEffect(() => {
        const fetchGuideDetails = async () => {
            const details = await GetGudeDetailsByGuideId(currentGuideId);
            if (details) {
                setGuide(details)
                setGuideStatus(details.GuideDetails.GuideStatus);
                setIsPublished(details.GuideDetails.GuideStatus === "Active");
                setIsUnPublished(details.GuideDetails.GuideStatus === "InActive");
                setOrganizationId(details.GuideDetails.OrganizationId);
                setguideType(details.GuideDetails.GuideType);
                setGuideName(details.GuideDetails.Name);
                setAccountId(details.GuideDetails.AccountId);
                setUpdatedBy(details.GuideDetails.UpdatedBy);
                setCreatedBy(details.GuideDetails.CreatedBy);
                setGuidestep(details.GuideDetails.GuideStep);
                setDescriptionValue(details.GuideDetails.Description);
                setSelectedFrequency(details.GuideDetails.Frequency);
                setIsAutoTriggerEnabled(details.GuideDetails.AutoTrigger);
                setPublishOption(details.GuideDetails.publishOption);
                setUnpublishOption(details.GuideDetails.unpublishOption);
                setTargetUrl(details.GuideDetails.TargetUrl);
                setCreatedDate(details.GuideDetails.CreatedDate);
                setPublishDate(details.GuideDetails.PublishDate);
                setUnPublishDate(details.GuideDetails.UnpublishDate);
                navigate(location.pathname, {
                    state: { response: details },
                    replace: true,
                  });
            }
        };

        fetchGuideDetails();
    }, [currentGuideId, guideStatus]);

    const HandlePublishToggle = async () => {
        try {
            if (!["Account Admin", "Publisher"].some(role => roles.includes(role)))
            {
                return;
            }
            await handleFinalSaveClick();

            if (isPublished) {
                const result = await UnPublishGuide(currentGuideId);
                if (result.Success) {
                    openSnackbar(`${guideName} ${translate(guideType)} ${translate("Unpublished Successfully")}`, "success");
                    setGuideStatus("InActive");
                    setIsPublished(false);
                    setIsUnPublished(true);
                    // Fetch and update the latest guide details
                    const updatedDetails = await GetGudeDetailsByGuideId(currentGuideId);
                    if (updatedDetails) {
                        setGuide(updatedDetails);
                        setGuideStatus(updatedDetails.GuideDetails.GuideStatus);
                        setIsPublished(updatedDetails.GuideDetails.GuideStatus === "Active");
                        setIsUnPublished(updatedDetails.GuideDetails.GuideStatus === "InActive");
                    }
                } else {
                    openSnackbar(translate(result.SuccessMessage), "error");
                }
            } else {

                const result = await PublishGuide(currentGuideId);
                if (result.Success) {
                    openSnackbar(`${guideName} ${translate(guideType)} ${translate("Published Successfully")}`, "success");
                    setGuideStatus("Active");
                    setIsPublished(true);
                    setIsUnPublished(false);
                    // Fetch and update the latest guide details
                    const updatedDetails = await GetGudeDetailsByGuideId(currentGuideId);
                    if (updatedDetails) {
                        setGuide(updatedDetails);
                        setGuideStatus(updatedDetails.GuideDetails.GuideStatus);
                        setIsPublished(updatedDetails.GuideDetails.GuideStatus === "Active");
                        setIsUnPublished(updatedDetails.GuideDetails.GuideStatus === "InActive");
                    }
                } else {
                    openSnackbar(translate(result.SuccessMessage), "error");
                }
            }

        } catch (error) {
            console.error('Error updating guide status:', error);
        }
    };




    const handleBackClick = () => {
        const GuideType: any = `${guideType.toLowerCase()}s`;
        navigate(`/${GuideType}`);
    };
    const handleEditClick = () => {
        setOriginalGuideName(guideName);
        setIsEditing(true);
    };
    const handleSave = async () => {
        setIsEditing(false);
        try {
            const result = await UpdateGuidName(currentGuideId, organizationId, guideName, accountId, guideType);
            if (result.Success === true) {
                openSnackbar(translate(result.SuccessMessage), "success");
            } else {
                openSnackbar(translate(result.ErrorMessage), "error");
            }
        } catch (error) {
            console.error('Error updating guide name:', error);
        }
    };
    const handleDrawClick = () => {
        if (targetUrl) {
            window.open(targetUrl, '_blank');
        }

    };
    useEffect(() => {
        if (guide?.PageTargets) {
            const mappedTriggers = guide?.PageTargets.map((trigger: any) => ({
                pageRule: trigger.Condition,
                url: trigger.Value,
                logicalOperator: trigger.Operator,
                PageTargetId: trigger.PageTargetId,
            }));
            setTriggers(mappedTriggers);
        }
    }, [guide?.PageTargets]);

    useEffect(() => {
        if (triggers.length === 0) {
            setTriggers([{ pageRule: "Equals", url: guide?.GuideDetails.TargetUrl || '', PageTargetId: "", logicalOperator: "" }]);
        }
    }, [triggers]);

    const handleAddTrigger = () => {
        if (triggers.length < 5) {
            setTriggers([...triggers, { pageRule: "Equals", url: "", PageTargetId: "", logicalOperator: "OR" }]);
            setPageTargetsSaved(false);
            setNewTriggersAdded(true);
            setErrorMessage([]);
        }
    };
    const pageRuleOptions = [
        { value: "Equals", label: translate("Equals") },
        { value: "Not Equals", label: translate("Not Equals") },
        { value: "Starts With", label: translate("Starts With") },
        { value: "Ends With", label: translate("Ends With") },
        { value: "Contains", label: translate("Contains") },
        { value: "Not Contains", label: translate("Does Not Contain") },
    ];

    const handleTriggerChange = (index: number, field: keyof Trigger, value: string) => {
        const newErrors = [];
        const updatedTriggers = [...triggers];
        updatedTriggers[index][field] = value;
        let error = "";    
        if (value.length < 1 && field === 'url')  {
            error = translate("Minimum Length : 1 Character");
        } else if (value.length > 500 ) {
            error = translate("Maximum Length : 500 characters.");
        } else if (/\s/.test(value) &&  field === 'url') {
            error = translate("Restriction : Spaces are not allowed");
        } 
        else {
            setTriggers(updatedTriggers);
        }
        if (error.length > 0) { newErrors[index] = error };
        setErrorMessage(newErrors); 
        setHasChanges(true);

    };
    const parseFrequency = (frequency: any) => {
        if (frequency?.startsWith('onceIn') && frequency !== "onceInSession") {
            const parts = frequency.split('-');
            const base = parts[0];
            const dropdownValue = parts.length > 1 ? parts[1] : '';
            return { frequencyBase: base, dropdownValue };
        }
        return { frequencyBase: frequency, dropdownValue: '' };
    };
    useEffect(() => {
        const initialFrequency = guide?.GuideDetails?.Frequency;
        if (initialFrequency !== "onceInSession") {
            const { frequencyBase, dropdownValue } = parseFrequency(initialFrequency);
            setSelectedFrequency(frequencyBase);
            setfrequencyDropdown(dropdownValue);
        }
    }, [location.state]);
    const [originalGuideName, setOriginalGuideName] = useState("");
    const [errors, setErrors] = useState<Partial<Record<string, string>>>({});

    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
        const { name, value } = event.target;
        let error = "";

        if (name === "GuideName" && value.trim().length < 3) {
            error = translate("Guide Name must be at least 3 characters.");
        }
        setGuideName(event.target.value)
        setErrors((prev) => ({ ...prev, [name]: error }));
    };
    useEffect(() => {
        if (isAutoTriggerEnabled) {
            setSelectedFrequency('onceInSession');
        }
    }, [isAutoTriggerEnabled]);

    const handleFrequencyChange = (value: string) => {
        if (value === "onceIn") {
            setSelectedFrequency(`onceIn${frequencyDropdown}`)
        } else {
            setSelectedFrequency(value);
        }
        if (value !== "onceIn") {
            setfrequencyDropdown("");
        }
    };
    const handleFrequencyDropdown = (e: any) => {
        const value = e.target.value;
        if (selectedFrequency?.startsWith("onceIn") && selectedFrequency !== "onceInSession") {
            setfrequencyDropdown(value);
            setSelectedFrequency(`onceIn-${value}`);
        }
    };

    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
        if (event.key === 'Enter') {
            handleSave();
        } else if (event.key === 'Escape') {
            setGuideName(originalGuideName);
            setIsEditing(false);
        }
    };
    const handleDeleteTrigger = async () => {
        if (deleteIndex === null) return; // No index to delete

        const triggerToDelete = triggers[deleteIndex];

        try {
            if (triggerToDelete.PageTargetId) {
                const pageTargetId = triggerToDelete.PageTargetId;
                const reqObj = {
                    currentGuideId,
                    pageTargetId,
                };
                const response = await DeletePageTarget(reqObj);
                if (response.Success) {
                    openSnackbar(translate(response.SuccessMessage), "success");
                    const updatedTriggers = triggers.filter((_, i) => i !== deleteIndex);
                    setTriggers(updatedTriggers);
                } else {
                    openSnackbar(translate(response.ErrorMessage), "error");
                }
            } else {
                if (triggers.length > 1) {
                    const updatedTriggers = triggers.filter((_, i) => i !== deleteIndex);
                    setTriggers(updatedTriggers);
                }
            }
        } catch (error) {
            console.error("Error deleting page target:", error);
            openSnackbar(translate("Failed to delete the trigger. Please try again."), "error");
        } finally {
            // Reset the state regardless of the outcome
            setDeleteIndex(null);
            setDialogOpen(false);
        }
    };

    const handleOperatorChange = (index: number, value: string) => {
        const updatedTriggers = [...triggers];

        // Since logicalOperator is for the next trigger, update the next trigger's operator
        if (index < updatedTriggers.length - 1) {
            updatedTriggers[index + 1].logicalOperator = value; // Update the next trigger's operator
        }

        setTriggers(updatedTriggers);
    };

    const [originalPageTargets, setOriginalPageTargets] = useState<any[]>(guide?.PageTargets || []);

    useEffect(() => {
        if (guide?.PageTargets && guide?.PageTargets.length > 0) {
            setOriginalPageTargets(guide?.PageTargets);
        }
    }, [guide?.PageTargets]);
    const handleSavePageTriggers = async () => {
        setHasChanges(false)
        const newTriggers = triggers.filter(trigger => !trigger.PageTargetId);
        const existingTriggers = triggers.filter(trigger => trigger.PageTargetId);

        if (newTriggers.length > 0) {
            const formattedNewPageTargets = newTriggers.map((trigger) => ({
                PageTargetId: "",
                Condition: trigger.pageRule,
                Operator: trigger?.logicalOperator || 'OR',
                Value: trigger.url,
                GuideId: currentGuideId,
                OrganizationId: "",
            }));
            try {
                const response = await SavePageTarget(formattedNewPageTargets);
                if (response.Success) {
                    openSnackbar(translate(response.SuccessMessage), "success");
                    setPageTargetsSaved(true);
                    setTriggers(prev => prev.filter(trigger => !!trigger.PageTargetId));
                    setNewTriggersAdded(false);
                    
                } else {
                    openSnackbar(translate(response.ErrorMessage), "error");
                }
            } catch (error) {
                console.error("Error saving new page targets:", error);
            }
        }

        // Updating existing page targets
        const modifiedTriggers = existingTriggers.filter((trigger) => {
            const originalTrigger = originalPageTargets.find(
                (origTrigger: any) => origTrigger.PageTargetId === trigger.PageTargetId
            );

            return originalTrigger && (
                originalTrigger.Condition !== trigger.pageRule ||
                originalTrigger.Operator !== trigger.logicalOperator ||
                originalTrigger.Value !== trigger.url
            );
        });

        if (modifiedTriggers.length > 0) {
            const formattedExistingPageTargets = modifiedTriggers.map((trigger) => ({
                PageTargetId: trigger.PageTargetId,
                Condition: trigger.pageRule,
                Operator: trigger?.logicalOperator || 'OR',
                Value: trigger.url,
                GuideId: currentGuideId,
                OrganizationId: "",
            }));

            try {
                const response = await UpdatePageTarget(formattedExistingPageTargets);
                if (response.Success) {
                    openSnackbar(translate(response.SuccessMessage), "success");
                    setPageTargetsSaved(true);
                    setNewTriggersAdded(false);
                } else {
                    openSnackbar(translate(response.ErrorMessage), "error");
                }
            } catch (error) {
                console.error("Error updating existing page targets:", error);
            }
        }

        try {
            const pageTargetsResponse = await GetPageTargets(currentGuideId);
            if (pageTargetsResponse) {
                // Map the response to a format suitable for your triggers
                const updatedPageTargets = pageTargetsResponse.map((trigger: any) => ({
                    pageRule: trigger.Condition,
                    url: trigger.Value,
                    logicalOperator: trigger.Operator,
                    PageTargetId: trigger.PageTargetId,
                }));

                // Combine the existing targets and new targets
                const combinedTriggers = [...updatedPageTargets];

                // Use a Set to keep track of seen URLs
                // const seenUrls = new Set();
                // const uniqueTriggers = combinedTriggers.filter(trigger => {
                //     // If the URL is not in the seen set, add it and return true to keep the trigger
                //     if (!seenUrls.has(trigger.url)) {
                //         seenUrls.add(trigger.url);
                //         return true; // Keep this trigger
                //     }
                //     return false; // Ignore this trigger (duplicate)
                // });

                // Set the original page targets and the unique triggers
                setOriginalPageTargets(pageTargetsResponse);
                setTriggers(combinedTriggers);
            } else {
                openSnackbar(translate(pageTargetsResponse.ErrorMessage), "error");
            }
        } catch (error) {
            console.error("Error fetching page targets:", error);
        }
    };


    const handleFinalSaveClick = async () => {
        // Determine the guide status based on changes and current state
        let newStatus = guideStatus;
        
        // If the guide was inactive (isUnPublished) but changes were made, set to Draft
        if (isUnPublished || isPublished && (hasChanges || newTriggersAdded || isEditing || Descriptionvalue !== guide?.GuideDetails?.Description)) {
            newStatus = 'Draft';
        } else if (isPublished) {
            newStatus = 'Active';
        } else if (isUnPublished) {
            newStatus = 'InActive';
        } else {
            newStatus = 'Draft';
        }
    
        // Update the state
        setGuideStatus(newStatus);
    
        const newGuide = {
            GuideId: currentGuideId,
            GuideType: guideType,
            Name: guideName.trim(),
            Content: `${guideType} content`,
            OrganizationId: organizationId,
            CreatedDate: CreatedDate,
            UpdatedDate: new Date().toISOString(),
            CreatedBy: createdBy,
            UpdatedBy: updatedBy,
            Frequency: "onceInSession",
            Segment: "All users",
            AccountId: accountId,
            GuideStep: guidestep,
            GuideStatus: newStatus, // Using newly determined status
            AutoTrigger: true,
            PublishType: publishOption,
            UnPublishType: unpublishOption,
            Description: Descriptionvalue,
            TargetUrl: targetUrl,
             //  PublishDate: publishOption === 'custom' ? customPublishDate :  new Date().toISOString(),
            //  UnpublishDate: unpublishOption === 'custom' ? customUnPublishDate :  new Date().toISOString(),
            ...((newStatus === 'Draft' || newStatus === 'InActive') && {
                PublishDate: publishOption === 'custom'
                    ? PublishDate
                    : currentDate,
                UnPublishDate: unpublishOption === 'custom'
                    ? UnPublishDate
                    : null,
            }),
    
            ...(isPublished && {
                UnpublishDate: unpublishOption === 'custom'
                    ? UnPublishDate
                    : null,
                PublishDate: publishOption === 'custom'
                    ? PublishDate
                    : currentDate,
            }),
        };
    
        if (!pageTargetsSaved && newTriggersAdded) {
            await handleSavePageTriggers();
        }       
        const response = await SubmitUpdateGuid(newGuide);
    
        if (response.Success === true) {
            openSnackbar(`${guideName} ${translate(guideType)} ${translate("updated Successfully")}`, "success");
            setPageTargetsSaved(false);
            setNewTriggersAdded(false);
            
            // Update states to reflect the new status
            setIsUnPublished(newStatus === 'InActive');
        } else {
            openSnackbar(translate(response.ErrorMessage), "error");
        }
        setIsEditing(false);
    };

useEffect(() => {
    if (
        guide?.GuideDetails.GuideStatus === "Active" &&
        guide?.GuideDetails.UnPublishType === "custom" &&
        guide?.GuideDetails.UnPublishDate
    ) {
        const interval = setInterval(async () => {
            const now = new Date();
            const unpublishDate = guide.GuideDetails.UnPublishDate ? new Date(guide.GuideDetails.UnPublishDate) : null;
            if (unpublishDate && now >= unpublishDate) {
                await HandlePublishToggle();
                const details = await GetGudeDetailsByGuideId(currentGuideId);
                if (details) {
                    setGuide(details);
                    setGuideStatus(details.GuideDetails.GuideStatus);
                    setIsPublished(details.GuideDetails.GuideStatus === "Active");
                    setIsUnPublished(details.GuideDetails.GuideStatus === "InActive");
                }
                clearInterval(interval);
            }
        }, 10);
        return () => clearInterval(interval);
    }
}, [guide]);

const { isExtensionInstalled, checkExtensionStatus } = useExtension();
    const [showExtensionRequiredPopup, setShowExtensionRequiredPopup] = useState(false);

    const handleEditInBuilder = () => {
        checkExtensionStatus();
        setTimeout(() => {
            if (isExtensionInstalled) {
                openGuideInBuilder(targetUrl, currentGuideId, accountId);
            } else {
                setShowExtensionRequiredPopup(true);
            }
        }, 100);
    };

    return (
        <Container maxWidth="xl">
            <div className='qadpt-web'>
                <div className='qadpt-webcontent'>
                    <div className='qadpt-setting-title'>
                        <div className='qadpt-backbtn'>
                            <Grid container alignItems="center" className='qadpt-titsec-grid'>
                                <Grid item>
                                    <IconButton onClick={handleBackClick}>
                                        <ArrowBackIcon />
                                    </IconButton>
                                </Grid>

                                <Grid item>
                                    <Typography variant="body1" className='qadpt-back-text' onClick={handleBackClick}>
                                        {translate('Back to')} {`${translate(guideType)}`}
                                    </Typography>
                                </Grid>
                            </Grid>
                        </div>

                        <div>
                            <Grid container className='qadpt-titsec'>
                                <Grid item>
                                    <Box className='qadpt-name-box'>
                                        {isEditing ? (
                                            <TextField
                                                name="GuideName"
                                                variant="outlined"
                                                value={guideName}
                                                onChange={handleChange}
                                                onKeyDown={handleKeyDown}
                                                helperText={errors.GuideName}
                                                error={!!errors.GuideName}
                                                sx={{ marginRight: '16px', width: '300px' }}
                                            />
                                        ) : (
                                            <Typography variant="h5" fontWeight="bold" className='qadpt-name-text'>
                                                {guideName}
                                            </Typography>
                                        )}
                                        <>{
                                            ["Account Admin", "Editor"].some(role => roles.includes(role)) &&
                                            <IconButton onClick={isEditing ? handleSave : handleEditClick}
                                                disabled={isEditing && !!errors.GuideName}
                                            >
                                                {isEditing ? (<Tooltip
                                                    arrow
                                                    title={translate("Save")}

                                                >
                                                    <img src={Saveicon} alt='saveicon' />
                                                </Tooltip>
                                                ) : (
                                                    <Tooltip arrow title={translate("Edit")}>
                                                        <EditIcon />
                                                    </Tooltip>
                                            
                                                )}
                                            </IconButton>
                                        }</>
                                    </Box>
                                </Grid>

                                <Grid item>
                                    <Grid container spacing={1}>
                                        <Grid item>
                                            <Button
                                                variant="outlined"
                                                color="primary"
                                                className="qadpt-action-btn"
                                                startIcon={<img src={OpeninNewWindow} alt="openinnewwindow" />}
                                                onClick={handleEditInBuilder}
                                                disabled={ !["Account Admin","Editor"].some(role => roles.includes(role)) }
                                            >
                                                {translate("Edit")} {translate(guideType)}
                                            </Button>
                                        </Grid>
                                        <Grid item>
                                            <Button
                                                variant="outlined"
                                                onClick={handleFinalSaveClick}
                                                disabled={ !["Account Admin","Editor"].some(role => roles.includes(role)) }
                                                className="qadpt-action-btn"
                                                startIcon={<img src={Saveicon} alt="saveicon" />}
                                            >
                                                {translate("Save")}
                                            </Button>
                                        </Grid>
                                        <Grid item>
                                            <Button
                                                variant="contained"
                                                onClick={HandlePublishToggle}
                                                disabled={ !["Account Admin","Publisher"].some(role => roles.includes(role)) }
                                                className={`qadpt-action-btn qadpt-action-btn-primary ${isPublished ? 'qadpt-unpublish' : ''}`} startIcon={<img src={isPublished ? cloudoff : PublishIcon} alt={isPublished ? 'cloudoff' : 'PublishIcon'} />}
                                            >
                                                {isPublished ? translate('UnPublish') : translate('Publish')}
                                            </Button>
                                        </Grid>
                                        <Grid item>
                                            <Button
                                                variant="outlined"
                                                disabled={true}
                                                className="qadpt-action-btn qadpt-share"
                                                startIcon={<img src={ShareIcon} alt="Shareicon" />}
                                            />
                                        </Grid>
                                    </Grid>
                                </Grid>

                            </Grid>
                        </div>
                    </div>

                    {/* Description and Auto Trigger */}
                    <div className='qadpt-content'>
                        <div className='qadpt-set-left'>
                            <div className='qadpt-description'>
                                <Grid item xs={12} md={6}>
                                    <Card className='qadpt-card'>
                                        <CardContent>
                                            <label htmlFor="description" className="qadpt-label" >{translate("Description")}</label>
                                            <textarea id="description"
                                                value={Descriptionvalue}
                                                maxLength={500}
                                                placeholder={translate("Enter your description here (max 500 characters)")}
                                                onChange={(e) => setDescriptionValue(e.target.value)} />

                                            {/* <div className="character-count">
                    Count: {Descriptionvalue.length}/500
                </div> */}

                                        </CardContent>
                                    </Card>
                                </Grid>
                            </div>
                            <div className='qadpt-buildsteps'>
                                <Grid item xs={12} md={6}>
                                    <Card className='qadpt-card qadpt-buildcard'>
                                        <CardContent>
                                            <div className="qadpt-label">
                                                {translate("Build Steps")}
                                            </div>

					                {guidestep.length > 0 && (
					                    <Typography variant="body1" sx={{ marginTop: '10', fontSize: '1rem', color: '#555' }}>
					                        {guidestep.length} step{guidestep.length > 1 ? 's' : ''}
					                    </Typography>
					                )}
					                <div className="qadpt-build-content">
					                    {guidestep.length > 0 ? (
					                        guidestep.map((step: any, index: any) => (
					                            <Box key={step.StepId || index} className="qadpt-steps">
					                                <TextField
					                                    variant="outlined"
					                                    multiline
					                                    minRows={1}
					                                    maxRows={1}
					                                    fullWidth
					                                    tabIndex={ -1} // Added tabIndex="-1" to make it unfocusable via tab
					                                    inputProps={{ tabIndex: -1 }} // Make the input element unfocusable
					                                    sx={{
					                                        borderRadius: "8px",
					                                        '& .MuiOutlinedInput-root': {
					                                            '& fieldset': {
					                                                borderColor: 'transparent',
					                                            },
					                                            '&:hover fieldset': {
					                                                borderColor: 'transparent',
					                                            },
					                                            '&.Mui-focused fieldset': {
					                                                borderColor: 'transparent',
					                                            },
					                                        },
					                                        '& textarea': {
					                                            overflow: "hidden",
					                                            resize: "none",
					                                            paddingTop: "4px",
					                                            lineHeight: "1.2",
					                                        },
					                                    }}
					                                    InputProps={{
					                                        endAdornment: (
					                                            <InputAdornment position="end">
					                                                <IconButton tabIndex={ -1}> {/* Added tabIndex="-1" to IconButton */}
					                                                    <DrawOutlinedIcon />
					                                                </IconButton>
					                                            </InputAdornment>
					                                        ),
					                                        readOnly: true, // Added readOnly property to enforce read-only behavior
					                                    }}
					                                    defaultValue={`${step?.StepTitle || `Step-${index + 1}`}\n${targetUrl}`}
					                                />
					                            </Box>
					                        ))
					                    ) : (
					                        <Typography variant="body1" sx={{ marginTop: '16px', color: '#888' }}>
                                                            {translate("No steps available. Please check back later.")}
					                        </Typography>
					                    )}
					                </div>
					            </CardContent>
					        </Card>
					    </Grid>
					</div>

                        </div>

                        <div className='qadpt-set-right'>

                            {/* Page Targeting Section */}
                            <div className="qadpt-page-target">
                                <Grid item xs={12} md={6}>
                                    <Card className="qadpt-card qadpt-target">
                                        <CardContent>
                                            {/* Flex container for label, sublabel, and buttons */}
                                            <div className="qadpt-header-container">
                                                <div className="qadpt-label-sublabel">
                                                    <div className="qadpt-label">{translate("Page Targeting")}</div>
                                                    <div className="qadpt-sublabel">
                                                        {translate("Which pages should the guide be visible?")}
                                                    </div>
                                                </div>

                                                {/* Align Add Target and Save buttons on the same row */}
                                                <div>
                                                    <Button
                                                        variant="outlined"
                                                        className="qadpt-add-target-btn"
                                                        onClick={handleAddTrigger}
                                                        disabled={triggers.length >= 5}
                                                        startIcon={<AddOutlinedIcon />}
                                                    >
                                                        {translate("Add Target")}
                                                    </Button>

                                                    <IconButton onClick={handleSavePageTriggers} disabled={ !hasChanges || errorMessage.length > 0} className='qadpt-add-target-btn save'  style={{ opacity: !hasChanges || errorMessage.length > 0 ? 0.5 : 1 }} >                                                        
                                                        <img src={Saveicon} alt="saveicon"   style={{ opacity: !hasChanges || errorMessage.length > 0 ? 0.5 : 1 }} />
                                                    </IconButton>
                                                </div>
                                            </div>
                                            <div className='qadpt-conditions'>  
                                            {triggers.length > 1 && (
                                                    <FormControl className='qadpt-operator'>
                                                        <span>{translate("Condition")}</span>
                                                        <Select
                                                            value={'OR'}
                                                            disabled
                                                            onChange={(e) => handleOperatorChange(0, e.target.value)} // Adjust handler as needed
                                                        >
                                                            <MenuItem value="AND">{translate("AND")}</MenuItem>
                                                            <MenuItem value="OR">{translate("OR")}</MenuItem>
                                                        </Select>
                                                    </FormControl>
                                                )}    
                                                {triggers.map((trigger, index) => (  
                                                    <Grid container spacing={2} key={index}>
                                                       
                                                        <Grid item xs={6}>
                                                             <div>
                                                                <label className="qadpt-field-label">{translate("Page Rule")}</label>
                                                                <FormControl fullWidth>
                                                                    <Select
                                                                        value={trigger.pageRule}
                                                                        onChange={(e) => handleTriggerChange(index, 'pageRule', e.target.value)}
                                                                        renderValue={(selected) =>
                                                                            pageRuleOptions.find(opt => opt.value === selected)?.label || selected
                                                                        }
                                                                        sx={{
                                                                            "&:hover .MuiOutlinedInput-notchedOutline": {
                                                                                border: "1px solid #ccc",
                                                                            },
                                                                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                                                                                border: "1px solid #ccc",
                                                                            },
                                                                            "&.MuiOutlinedInput-notchedOutline": {
                                                                                height: "40px !important",
                                                                            },
                                                                        }}
                                                                    >
                                                                        {pageRuleOptions.map((option) => (
                                                                            <MenuItem
                                                                                key={option.value}
                                                                                value={option.value}
                                                                                sx={{ fontSize: '14px', display: 'flex', justifyContent: 'space-between' }}
                                                                            >
                                                                                {option.label}
                                                                                <img src={checksmall} alt="check" />
                                                                            </MenuItem>
                                                                        ))}
                                                                    </Select>
                                                                </FormControl>

</div>

                                                        </Grid>
                                                        <Grid item xs={5}>
                                                        <div>
                                                                <label className="qadpt-field-label">{translate("URL")}</label>
    <TextField
        fullWidth
        value={trigger.url}
        onChange={(e) => handleTriggerChange(index, 'url', e.target.value)}
        error={!!errorMessage[index]} 
        helperText={
            !!errorMessage[index] && (
                <span style={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <img src={Warning} alt="error-icon"/>
                    {errorMessage[index]}
                </span>
            )
        }
        InputProps={{
            sx: {
                "&:hover .MuiOutlinedInput-notchedOutline": {
                    border: "1px solid #ccc", // Border on hover
                },
              "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                border: "1px solid #ccc", // Removes the blue border on focus
                },
                "&.MuiOutlinedInput-notchedOutline": {
                    height :"40px !important",
                },
                "&.Mui-error .MuiOutlinedInput-notchedOutline": {
                borderColor: "#e6a957 !important", // Custom error border color
            },
            },
          }}
    />
</div>
                                                        </Grid>
                                                        <Grid item xs={1} className={`qadpt-btngrid ${!!errorMessage[index] ? 'qadpt-error-btn' : ''}`}  style={{ opacity: triggers.length === 1 ? 0.5 : 1 }}>
                                                            <IconButton onClick={() => {
                                                                setDeleteIndex(index); // Set the index of the trigger to delete
                                                                setDialogOpen(true); // Open the confirmation dialog
                                                            }} disabled={triggers.length === 1} >

                                                  <img 
                                                src={Targetdelete} 
                                                alt='tardelete' 
                                                style={{ opacity: triggers.length === 1 ? 0.5 : 1 }} 
                                                                />
                                                            </IconButton>
                                                        </Grid>
                                                        
                                                       
                                                    </Grid>
                                                ))}
                                                {/* <ConfirmationDialog
                                                    open={dialogOpen}
                                                    onClose={() => setDialogOpen(false)}
                                                    onConfirm={handleDeleteTrigger} // Call delete function on confirmation
                                                    title="Delete Trigger"
                                                    message="Are you sure you want to delete this trigger?"
                                                /> */}
 {/* {dialogOpen && (
  <div className="qadpt-modal-overlay">
    <div className="qadpt-usrconfirm-popup qadpt-danger qadpt-deltrigger">
      <div className="qadpt-popup-title">Delete Trigger</div>

      <div className="qadpt-warning">
        Are you sure you want to delete this trigger?
      </div>

      <div className="qadpt-buttons">
        <button
          onClick={() => setDialogOpen(false)}
          className="qadpt-cancel-button"
        >
          Cancel
        </button>

        <button
          onClick={handleDeleteTrigger}
          className="qadpt-conform-button"
          type="button"
        >
          Confirm
        </button>
      </div>
    </div>
  </div>
)} */}

        {dialogOpen && (
           <div className="qadpt-modal-overlay">       
        <div className="qadpt-usrconfirm-popup qadpt-danger">
          <div>
            <div className="qadpt-icon">      
              <IconButton
                                className="qadpt-svg">
                                    <i className='fal fa-trash-alt'></i>
                                    </IconButton>
            </div>
                                                            </div>                                                      <div className="qadpt-popup-title">
                                                                {translate("Delete Trigger")}
            </div>
      
            <div className="qadpt-warning">
                                                                {translate("Are you sure you want to delete this trigger?")}
      </div>
      
      <div className="qadpt-buttons">
        <button
          onClick={() => setDialogOpen(false)}
          className="qadpt-cancel-button"
        >
                                                                    {translate("Cancel")}
        </button>

        <button
          onClick={handleDeleteTrigger}
          className="qadpt-conform-button"
          type="button"
        >
                                                                    {translate("Confirm")}
        </button>
      </div>
                    </div>
                    </div>
      )}



                                            </div>

                                            
                                        </CardContent>
                                    </Card>
                                </Grid>
                            </div>

                            <div className='qadpt-auto-trigger'>
                                {/* Auto Trigger Section */}
                                <Grid item xs={12} md={6}>
                                    <Card className='qadpt-card qadpt-trigger'>
                                        <CardContent>
                                            <Grid container>
                                                <div className="qadpt-label">{translate("Auto Trigger")}</div>
                                                <Switch
                                                    checked={true}
                                                    onChange={(e) => setIsAutoTriggerEnabled(e.target.checked)}
                                                    disabled={true}
                                                />
                                            </Grid>
                                            <div className='qadpt-sublabel' style={{ lineHeight: 1 }}>
                                                {translate("Enable this to automatically display the")} {translate(guideType)} {translate("on target pages, or trigger manually via checklists.")}
                                            </div>
                                        </CardContent>
                                    </Card>
                                </Grid>
                            </div>


                            {/* Frequency Section */}
                            <div className='qadpt-frequency'>

                                <Grid item xs={12} md={6}>
                                    <Card className='qadpt-card'>
                                        <CardContent>
                                            <div className="qadpt-label">{translate("Frequency")}</div>
                                            <div className='qadpt-sublabel'>{translate("How often do you want it to trigger")}</div>
                                            <FormControl component="fieldset" disabled={!isAutoTriggerEnabled}>
                                                <Grid container spacing={1} alignItems="center" wrap="nowrap">
                                                    <Grid item>
                                                        <FormControlLabel
                                                            control={
                                                                <Radio
                                                                    checked={selectedFrequency === 'onlyOnce'}
                                                                    onChange={() => handleFrequencyChange('onlyOnce')}
                                                                    disabled
                                                                />
                                                            }
                                                            label={<span className="qadpt-freqselect">{translate("Only Once")}</span>}
                                                        />
                                                    </Grid>
                                                    <Grid item>
                                                        <FormControlLabel
                                                            control={
                                                                <Radio

                                                                    checked={true}
                                                                    onChange={() => handleFrequencyChange('onceInSession')}
                                                                />
                                                            }
                                                            label={<span className="qadpt-freqselect">{translate("Once in a session")}</span>}
                                                        />
                                                    </Grid>
                                                    <Grid item>
                                                        <FormControlLabel
                                                            control={
                                                                <Radio
                                                                    checked={selectedFrequency === 'onceADay'}
                                                                    onChange={() => handleFrequencyChange('onceADay')}
                                                                    disabled
                                                                />
                                                            }
                                                            label={<span className="qadpt-freqselect">{translate("Once a day")}</span>}
                                                        />
                                                    </Grid>
                                                    <Grid item>
                                                        <FormControlLabel
                                                            control={
                                                                <Radio
                                                                    checked={selectedFrequency?.startsWith('onceIn') && selectedFrequency !== 'onceInSession'}
                                                                    onChange={() => handleFrequencyChange('onceIn')}
                                                                    disabled
                                                                />
                                                            }
                                                            label={<span className="qadpt-freqselect">{translate("Once in")}</span>}
                                                        />
                                                    </Grid>
                                                    <Grid item>
                                                        <FormControl sx={{ marginLeft: '8px', minWidth: '80px' }} disabled={!isAutoTriggerEnabled || !selectedFrequency?.startsWith('onceIn')}>
                                                            <Select
                                                                labelId="frequency-select-label"
                                                                value={frequencyDropdown}
                                                                defaultValue='2days'
                                                                onChange={handleFrequencyDropdown}
                                                                disabled
                                                            >
                                                                <MenuItem value="2days">{translate("2 days")}</MenuItem>
                                                                <MenuItem value="3days">{translate("3 days")}</MenuItem>
                                                                <MenuItem value="Week">{translate("Week")}</MenuItem>
                                                                <MenuItem value="month">{translate("Month")}</MenuItem>
                                                                <MenuItem value="Quarter">{translate("Quarter")}</MenuItem>
                                                                <MenuItem value="Semi-Yearly">{translate("Semi-Yearly")}</MenuItem>
                                                                <MenuItem value="Yearly">{translate("Yearly")}</MenuItem>
                                                            </Select>
                                                        </FormControl>
                                                    </Grid>
                                                </Grid>
                                            </FormControl>


                                        </CardContent>
                                    </Card>
                                </Grid>

                            </div>

                            {/* Review & Publish Section */}
                            <div className='qadpt-rev-publish'>
                                <Grid item xs={12} md={6}>
                                    <Card className='qadpt-card qadpt-rev'>
                                        <CardContent>
                                            <div className="qadpt-label">{translate("Review & Publish")}</div>
                                            <Grid container spacing={2}>
                                                <Grid item xs={6} className='qadpt-gridleft'>
                                                    <Typography>{translate("Publish")} {`${translate(guideType)}`}:</Typography>
                                                    <FormControl component="fieldset">
                                                        <RadioGroup
                                                            value={publishOption}
                                                            onChange={(e) => setPublishOption(e.target.value)}
                                                        >
                                                            <FormControlLabel
                                                                value="immediately"
                                                                control={<Radio />}
                                                                label={translate("Immediately")}
                                                            />
                                                            <FormControlLabel
                                                                value="custom"
                                                                control={<Radio />}
                                                                label={translate("Custom date")}
                                                            />
                                                        </RadioGroup>
                                                    </FormControl>
                                                    {publishOption === 'custom' && (
                                                        <TextField
                                                            type="datetime-local"
                                                            value={PublishDate}
                                                            onChange={handleCustomDateChange}
                                                            fullWidth
                                                            sx={{ marginTop: '8px' }}
                                                            inputProps={{
                                                                min: currentDateTime,
                                                            }}
                                                        />
                                                    )}
                                                </Grid>

                                                <Grid item xs={6} className='qadpt-gridright'>
                                                    <Typography>{translate("UnPublish")} {`${translate(guideType)}`}:</Typography>
                                                    <FormControl component="fieldset">
                                                        <RadioGroup
                                                            value={unpublishOption}
                                                            onChange={(e) => setUnpublishOption(e.target.value)}
                                                        >
                                                            <FormControlLabel
                                                                value="Manually"
                                                                control={<Radio />}
                                                                label={translate("Manually")}
                                                            />
                                                            <FormControlLabel
                                                                value="custom"
                                                                control={<Radio />}
                                                                label={translate("Custom date")}

                                                            />
                                                        </RadioGroup>
                                                    </FormControl>
                                                    {unpublishOption === 'custom' && (
                                                        <TextField
                                                            type="datetime-local"
                                                            value={UnPublishDate} // Use value instead of defaultValue for controlled input
                                                            onChange={handleCustomDateChangeTwo} // Handle input changes
                                                            fullWidth
                                                            sx={{ marginTop: '8px' }}
                                                            inputProps={{
                                                                min: currentDateTime, 
                                                            }}
                                                        />
                                                    )}
                                                </Grid>
                                            </Grid>
                                        </CardContent>
                                    </Card>
                                </Grid>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            {showExtensionRequiredPopup && (
    <div style={{ position: 'fixed', top: 0, left: 0, width: '100vw', height: '100vh', backgroundColor: 'rgba(0,0,0,0.5)', zIndex: 999 }} />
)}
{showExtensionRequiredPopup && (
    <div style={{ position: 'fixed', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', zIndex: 1000, backgroundColor: 'white', borderRadius: '8px', boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)' }}>
        <ExtensionRequiredPopup
            setShowPopup={setShowExtensionRequiredPopup}
            showPopup={showExtensionRequiredPopup}
            name={guideType}
        />
    </div>
)}
        </Container>
    );
};

export default WebappSettings;
