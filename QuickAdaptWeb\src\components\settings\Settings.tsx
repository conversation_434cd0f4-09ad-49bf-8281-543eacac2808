import React, { useState, useEffect, useContext } from "react";
import { NavLink, useLocation } from "react-router-dom";
import { isSidebarOpen, subscribe } from "../adminMenu/sidemenustate";
import { Container, List, ListItem, ListItemText, Typography, Box } from "@mui/material";
import { useAuth } from "../auth/AuthProvider";
import { User } from "../../models/User";
import { useTranslation } from "react-i18next";
import { AccountContext } from "../account/AccountContext";


// import { createGlobalStyle } from 'styled-components';

// const GlobalStyle = createGlobalStyle<{ isHidden: boolean }>`
//   body {
//     overflow: ${({ isHidden }) => (isHidden ? 'hidden' : 'auto')};
//   }
// `;

const Settings = () => {
	const [sidebarOpen, setSidebarOpen] = useState(true);
	const location = useLocation();
	const isHidden = location.pathname === "/settings/team";
	const { signOut, userDetails } = useAuth();
	const [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId ?? "");
	const [userType, setUserType] = useState(userDetails?.UserType ?? "");
	const { roles } = useContext(AccountContext);

	const [user, setUser] = useState<User | null>(null);

	useEffect(() => {
		const unsubscribe = subscribe(setSidebarOpen);
		return () => unsubscribe();
	}, []);

	useEffect(() => {
		const userInfoString = localStorage.getItem("userInfo");
		if (userInfoString) {
			try {
				const userInfo = JSON.parse(userInfoString);
				if (userInfo['user']) {
					const parsedUser = JSON.parse(userInfo['user']);
					setUser(parsedUser);
					if (parsedUser) {
						const OrgId = parsedUser.OrganizationId ?? '';
						setOrganizationId(OrgId);
					}
				}
			} catch (error) {
				console.error("Error parsing userInfo: ", error);
			}
		}
		else if (userDetails) {
			setUser(userDetails);
			if (userDetails) {
				const OrgId = userDetails.OrganizationId ?? '';
				setOrganizationId(OrgId);
			}
		}
	}, []);

	const { t: translate } = useTranslation();
	return (
		<>
			<Container className="sidemenubar">
				<Box className={`qadpt-setsidebar ${sidebarOpen ? "sidebar-open" : "sidebar-closed"}`}>
					<div className="qadpt-sidebarlist">{translate('Settings')}</div>
					<List className="qadpt-sidebarele">
						{[
							{ text: translate('Team'), path: `/${OrganizationId}/team`, access: ['admin'],roles:[] },
							{
								text: translate('Roles'), path: `/${OrganizationId}/roles`, access: ['admin'],roles:[]
							},
							{ text: translate('Account'), path: `/${OrganizationId}/accounts`, access: ['admin'],roles:[] },

							{
								text: translate('Domain'), path: "/settings/domains",
								access: ['admin'],roles:[]
							},
							{
								text: translate('Rights'), path: "/settings/rights",
								access: ['admin', 'user'],roles:["Account Admin","Editor","Publisher"]
							},
							{
								text: translate('Alerts'), path: "/settings/alerts",
								access: ['admin', 'user'],roles:["Account Admin","Editor","Publisher"]
							},
							{
								text: translate('Billing'), path: "/settings/billing",
								access: ['admin'],roles:["Account Admin","Editor","Publisher"]
							},
							{
								text: translate('Installation'), path: "/settings/install",
								access: ['admin', 'user'],roles:["Account Admin"]
							},
							{
								text: translate('Activity Log'), path: "/settings/activitylog",
								access: ['admin'],roles:["Account Admin","Editor","Publisher"]
							},
							{
								text: translate('Agents'), path: "/settings/agents",
								access: ['admin', 'user'],roles:["Account Admin","Editor","Publisher"]
							},
							{
								text: translate('Training'), path: "/settings/training",
								access: ['admin', 'user'],roles:["Account Admin","Editor","Publisher"]
							},
						].map((item, index) => (<>
							{item.access.includes(userType.toLocaleLowerCase()) && (userType.toLocaleLowerCase() == "user" ? item.roles.some(role => roles?.includes(role)): true) &&
								<ListItem
									key={index}
									component={NavLink}
									to={item.path}
									className={`qadpt-sidebarinput ${location.pathname === item.path ||
											(item.path === "/settings/agents" && (location.pathname === "/settings/scripts" || location.pathname === "/settings/scripthistory" || location.pathname === "/settings/scripthistoryviewer"))
											? "active" : ""
										}`}
								>
									<ListItemText
										primary={
											<Typography
												className={`qadpt-sidebarval ${location.pathname === item.path ||
														(item.path === "/settings/agents" && (location.pathname === "/settings/scripts" || location.pathname === "/settings/scripthistory" || location.pathname === "/settings/scripthistoryviewer"))
														? "active" : ""
													}`}
											// sx={{
											// 	fontFamily: "Poppins",
											// 	fontSize: "14px",
											// 	fontWeight: 400,
											// 	lineHeight: "21px",
											// 	letterSpacing: "0.3px",
											// 	textAlign: "left",
											// 	color: location.pathname === item.path ? "#ffffff" : "#202224",
											// 	marginLeft: "-2px",
											// }}
											>
												{item.text}
											</Typography>
										}
									/>
								</ListItem>
							}
						</>
						))}
					</List>
				</Box>
			</Container>
		</>
	);
};

export default Settings;
