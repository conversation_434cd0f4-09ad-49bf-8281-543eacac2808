import React, { useState, useEffect, ChangeEvent, FocusEvent, FormEvent } from "react";
import {
	TextField,
	Button,
	Select,
	MenuItem,
	FormControl,
	InputLabel,
	FormHelperText,
	SelectChangeEvent,
	Snackbar,
	Alert,
	Box,
} from "@mui/material";
import { UnblockUser } from "../../services/UserService";
import { adminUrl } from "../../services/APIService";
import { useSnackbar } from "../../SnackbarContext";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import Visibility from "@mui/icons-material/Visibility";
import { JSEncrypt } from "jsencrypt";
import LockOpenIcon from "@mui/icons-material/LockOpen";
import styles from "./UserStyles.module.scss";
import { useAuth } from "../auth/AuthProvider";
import { useTranslation } from "react-i18next";

type InputFields = {
	Password: string;
};

type ErrorFields = Partial<InputFields>;

const UserUnblock = (props: any) => {
	const {
		unBlockUserPopup,
		setUnBlockUserPopup,
		userId,
		fetchUsersList,
		setModels,
		setLoading,
		skip,
		top,
		//Organizationid,
		setTotalcount,
		orderByFields,
		filters,
	} = props;
	const { openSnackbar } = useSnackbar();
	const { signOut, userDetails } = useAuth();
	const { t: translate } = useTranslation();
	const [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId??"");

	const handleSubmit = async (e: any) => {
		e.preventDefault();

		setLoading(true);
		try {
			await UnblockUser(
				setModels,
				setLoading,
				OrganizationId,
				skip,
				top,
				setTotalcount,
				userId,
				openSnackbar,
				orderByFields,
				filters
			);

			setLoading(false);
			setUnBlockUserPopup(false);
		} catch (error) {
		}
	};

	return (
		unBlockUserPopup && (
			<div className="qadpt-modal-overlay">
			<div className="qadpt-usrconfirm-popup qadpt-success">
  <div>
    <div className="qadpt-icon">
      <LockOpenIcon/>
    </div>
  </div>
  <div>
    <div className="qadpt-popup-title">{translate('Unblock user')}</div>
    <div className="qadpt-warning">
        {translate('Unblocking will take this user to its previous state')}
      </div>
      {/* <svg
        onClick={() => setUnBlockUserPopup(false)}
        className="close-icon"
        xmlns="http://www.w3.org/2000/svg"
        x="0px"
        y="0px"
        width="24"
        height="24"
        viewBox="0 0 50 50"
      >
        <path d="M 7.71875 6.28125 L 6.28125 7.71875 L 23.5625 25 L 6.28125 42.28125 L 7.71875 43.71875 L 25 26.4375 L 42.28125 43.71875 L 43.71875 42.28125 L 26.4375 25 L 43.71875 7.71875 L 42.28125 6.28125 L 25 23.5625 Z"></path>
      </svg>
    </div> */}
    <form onSubmit={handleSubmit}>
      <div className="qadpt-buttons">
        <button
          onClick={() => setUnBlockUserPopup(false)}
          className="qadpt-cancel-button"
        >
          {translate('Cancel')}
        </button>
        <button
          className="qadpt-conform-button"
          type="submit"
        >
          {translate('Unblock')}
        </button>
      </div>
    </form>
  </div>
				</div>
				</div>

		)
	);
};

export default UserUnblock;
