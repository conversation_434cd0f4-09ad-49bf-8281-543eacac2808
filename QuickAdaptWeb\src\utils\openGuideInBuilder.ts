export function openGuideInBuilder(targetUrl: string, guideId: string, accountId?: string) {
  if (!targetUrl || !guideId) return;
  const hasQuery = targetUrl.includes('?');
  const params = [`quickadopt_guide_id=${encodeURIComponent(guideId)}`];
  if (accountId) {
    params.push(`qa_account_id=${encodeURIComponent(accountId)}`);
  }
  const paramString = params.join('&');
  const url = hasQuery ? `${targetUrl}&${paramString}` : `${targetUrl}?${paramString}`;
  window.open(url, '_blank');
} 