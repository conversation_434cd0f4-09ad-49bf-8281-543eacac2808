{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptWeb\\\\src\\\\components\\\\adminMenu\\\\sideMenu.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Dashboard, Audience, Tours, Announcements, Tooltips, webbanner, Checklist, Surveys, Hotspots, Settings, Filemanagement } from \"../../assets/icons/icons\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { ThemeProvider, createTheme } from \"@mui/material/styles\";\nimport CssBaseline from \"@mui/material/CssBaseline\";\nimport Drawer from \"@mui/material/Drawer\";\nimport List from \"@mui/material/List\";\nimport ListItemIcon from \"@mui/material/ListItemIcon\";\nimport Box from \"@mui/material/Box\";\nimport Typography from \"@mui/material/Typography\";\nimport Popup from \"../common/Popup\";\nimport ButtonBase from '@mui/material/ButtonBase';\nimport { isSidebarOpen, setSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\n// import { useRtl } from \"../../RtlContext\";\n\nimport PerfectScrollbar from 'react-perfect-scrollbar';\nimport 'react-perfect-scrollbar/dist/css/styles.css';\nimport { useAuth } from \"../auth/AuthProvider\";\nimport { useTranslation } from \"react-i18next\";\nimport { useRtl } from \"../../RtlContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CursorTooltip = ({\n  title,\n  children\n}) => {\n  _s();\n  const {\n    isRtl\n  } = useRtl();\n  const [visible, setVisible] = useState(false);\n  const [showTooltip, setShowTooltip] = useState(false);\n  const [position, setPosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const timerRef = React.useRef(null);\n  const handleMouseEnter = () => {\n    timerRef.current = window.setTimeout(() => {\n      setShowTooltip(true);\n      setVisible(true);\n      document.addEventListener('mousemove', handleMouseMove);\n    }, 600);\n  };\n  const handleMouseLeave = () => {\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n      timerRef.current = null;\n    }\n    setVisible(false);\n    setShowTooltip(false);\n    document.removeEventListener('mousemove', handleMouseMove);\n  };\n  const handleMouseMove = e => {\n    const gap = 15;\n    setPosition({\n      x: e.clientX + gap,\n      y: e.clientY + gap\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"span\", {\n    onMouseEnter: handleMouseEnter,\n    onMouseLeave: handleMouseLeave,\n    children: [children, showTooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        ...(isRtl ? {\n          right: `${window.innerWidth - position.x}px`\n        } : {\n          left: `${position.x}px`\n        }),\n        top: `${position.y}px`,\n        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n        backgroundColor: '#fff',\n        color: '#000',\n        fontSize: '12px',\n        borderRadius: '8px',\n        padding: '16px',\n        borderTop: '2px solid #5c80ac',\n        zIndex: 9999,\n        pointerEvents: 'none',\n        opacity: visible ? 1 : 0,\n        width: visible ? '248px' : '0px',\n        transition: 'all 0.5s ease',\n        overflow: 'hidden',\n        transform: visible ? 'scale(1)' : 'scale(0.9)'\n      },\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(CursorTooltip, \"15H8YyNLIdilWvQsXegrAAIclqg=\", false, function () {\n  return [useRtl];\n});\n_c = CursorTooltip;\nconst SideMenu = ({\n  selectedLanguageProp\n}) => {\n  _s2();\n  var _userDetails$UserType;\n  const {\n    t: translate\n  } = useTranslation();\n  const [activeItem, setActiveItem] = useState(\"\");\n  const [openPopup, setOpenPopup] = useState(false);\n  const [sidebarOpen, setLocalSidebarOpen] = useState(isSidebarOpen());\n  const [translatedTitles, setTranslatedTitles] = useState({});\n  const {\n    signOut,\n    userDetails\n  } = useAuth();\n  const [OrganizationId, setOrganizationId] = useState(userDetails === null || userDetails === void 0 ? void 0 : userDetails.OrganizationId);\n  const [user, setUser] = useState(null);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [userType, setUserType] = useState((_userDetails$UserType = userDetails === null || userDetails === void 0 ? void 0 : userDetails.UserType) !== null && _userDetails$UserType !== void 0 ? _userDetails$UserType : \"\");\n  const {\n    isRtl\n  } = useRtl();\n  const themeWithDirection = createTheme({\n    direction: isRtl ? \"rtl\" : \"ltr\"\n  });\n  useEffect(() => {\n    const userInfoString = localStorage.getItem(\"userInfo\");\n    if (userInfoString) {\n      try {\n        const userInfo = JSON.parse(userInfoString);\n        if (userInfo['user']) {\n          const parsedUser = JSON.parse(userInfo['user']);\n          setUser(parsedUser);\n          if (parsedUser) {\n            var _parsedUser$Organizat;\n            const OrgId = (_parsedUser$Organizat = parsedUser.OrganizationId) !== null && _parsedUser$Organizat !== void 0 ? _parsedUser$Organizat : '';\n            setOrganizationId(OrgId);\n          }\n        }\n      } catch (error) {\n        console.error(\"Error parsing userInfo: \", error);\n      }\n    } else if (userDetails) {\n      setUser(userDetails);\n      if (userDetails) {\n        var _userDetails$Organiza;\n        const OrgId = (_userDetails$Organiza = userDetails.OrganizationId) !== null && _userDetails$Organiza !== void 0 ? _userDetails$Organiza : '';\n        setOrganizationId(OrgId);\n      }\n    }\n  }, []);\n  useEffect(() => {\n    const unsubscribe = subscribe(setLocalSidebarOpen);\n    return () => unsubscribe();\n  }, []);\n  const MenuClick = key => {\n    if (!OrganizationId) {\n      const userInfoString = localStorage.getItem(\"userInfo\");\n      if (userInfoString) {\n        try {\n          const userInfo = JSON.parse(userInfoString);\n          if (userInfo['user']) {\n            const parsedUser = JSON.parse(userInfo['user']);\n            setUser(parsedUser);\n            if (parsedUser) {\n              var _parsedUser$Organizat2;\n              const OrgId = (_parsedUser$Organizat2 = parsedUser.OrganizationId) !== null && _parsedUser$Organizat2 !== void 0 ? _parsedUser$Organizat2 : '';\n              setOrganizationId(OrgId);\n            }\n          }\n        } catch (error) {\n          console.error(\"Error parsing userInfo: \", error);\n        }\n      }\n    }\n    setActiveItem(key);\n    switch (key.toLowerCase()) {\n      case \"dashboard\":\n        return navigate(\"/dashboard\");\n      case \"audience\":\n        return navigate(\"/audience\");\n      case \"tours\":\n        return navigate(\"/tours\");\n      case \"announcements\":\n        return navigate(\"/announcements\");\n      case \"tooltips\":\n        return navigate(\"/tooltips\");\n      case \"banners\":\n        return navigate(\"/banners\");\n      case \"checklists\":\n        return navigate(\"/checklists\");\n      case \"surveys\":\n        return navigate(\"/surveys\");\n      case \"hotspots\":\n        return navigate(\"/hotspots\");\n      case \"settings\":\n        if (userType.toLocaleLowerCase() == \"admin\") return navigate(`${OrganizationId}/team`);else return navigate(`settings/rights`);\n      case 'filemanagement':\n        return navigate(\"/filelist\");\n      default:\n        return navigate(\"/\");\n    }\n  };\n  const toggleSidebar = () => {\n    setLocalSidebarOpen(!sidebarOpen);\n    setSidebarOpen(!sidebarOpen);\n  };\n  if (location.pathname === \"/Builder\") {\n    return null;\n  }\n  const menuItems = [{\n    key: \"Announcements\",\n    icon: Announcements,\n    title: translate('Announcements'),\n    tooltip: translate('Helps to communicate important updates, notifications, or messages.'),\n    access: ['admin,user']\n  }, {\n    key: \"Banners\",\n    icon: webbanner,\n    title: translate('Banners'),\n    tooltip: translate('Displays notifications at the top of the screen.'),\n    access: ['admin,user']\n  }, {\n    key: \"Tooltips\",\n    icon: Tooltips,\n    title: translate('Tooltips'),\n    tooltip: translate('Provide quick explanations, tips, or instructions of the tools,'),\n    access: ['admin,user']\n  }, {\n    key: \"Hotspots\",\n    icon: Hotspots,\n    title: translate('Hotspots'),\n    tooltip: translate('Interactive areas to draw attention to important features, actions, or guidance.'),\n    access: ['admin,user']\n  }, {\n    key: \"Tours\",\n    icon: Tours,\n    title: translate('Product Tour'),\n    tooltip: translate('Step-by-step guides to navigate and understand key features.'),\n    access: ['admin,user']\n  }, {\n    key: \"Checklists\",\n    icon: Checklist,\n    title: translate('Checklists'),\n    tooltip: translate('Task lists that guide users through a series of steps or actions.'),\n    access: ['admin,user']\n  }, {\n    key: \"Filemanagement\",\n    icon: Filemanagement,\n    title: translate('File Management'),\n    tooltip: translate('Store and reuse media assets across multiple interactions.'),\n    access: ['user']\n  }, {\n    key: \"Settings\",\n    icon: Settings,\n    title: translate('Settings'),\n    tooltip: translate('Configure application settings and preferences.'),\n    access: ['admin,user']\n  }, {\n    key: \"Surveys\",\n    icon: Surveys,\n    title: translate('Surveys'),\n    disabled: true,\n    tooltip: translate('Interactive forms or questionnaires designed to collect feedback, insights, or opinions'),\n    access: ['admin,user']\n  }, {\n    key: \"Dashboard\",\n    icon: Dashboard,\n    title: translate('Dashboard'),\n    disabled: true,\n    tooltip: translate('Get a unified view of insights and actions in one place.'),\n    access: ['admin,user']\n  }, {\n    key: \"Audience\",\n    icon: Audience,\n    title: translate('Audience'),\n    disabled: true,\n    tooltip: translate('Categorize users into segments for precise targeting.'),\n    access: ['admin,user']\n  }];\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: themeWithDirection,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-side-menu\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Drawer, {\n          variant: \"persistent\",\n          anchor: isRtl ? \"right\" : \"left\",\n          open: sidebarOpen,\n          className: \"qadpt-smenu\",\n          sx: {\n            \"& .MuiDrawer-paper\": {\n              direction: isRtl ? \"rtl\" : \"ltr\"\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(PerfectScrollbar, {\n            children: /*#__PURE__*/_jsxDEV(List, {\n              className: \"qadpt-smenu-list\",\n              children: [menuItems.map(({\n                key,\n                icon,\n                title,\n                disabled,\n                tooltip,\n                access\n              }, index) => !disabled && !access.includes(userType.toLocaleLowerCase()) && /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: /*#__PURE__*/_jsxDEV(CursorTooltip, {\n                  title: tooltip,\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: /*#__PURE__*/_jsxDEV(ButtonBase, {\n                      className: `qadpt-sm-item ${activeItem === key ? 'active' : 'inactive'} ${disabled ? 'disabled' : ''}`,\n                      onClick: () => MenuClick(key),\n                      disabled: disabled,\n                      disableRipple: true,\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          textAlign: 'left',\n                          width: '100%'\n                        },\n                        children: !disabled && /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            opacity: disabled ? 0.5 : 1,\n                            '& p': {\n                              fontSize: '14px'\n                            }\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                            className: `qadpt-sm-icon ${activeItem === key ? 'active' : 'inactive'}`,\n                            sx: {\n                              ...(key === 'Banners' && {\n                                marginLeft: '-3px'\n                              })\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"img\", {\n                              src: icon,\n                              alt: key\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 290,\n                              columnNumber: 29\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 284,\n                            columnNumber: 27\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            className: \"qadpt-sm-text\",\n                            children: translatedTitles[title] || title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 293,\n                            columnNumber: 27\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 274,\n                          columnNumber: 25\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 272,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 17\n                }, this)\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 15\n              }, this)), /*#__PURE__*/_jsxDEV(Box, {\n                className: \"qadpt-cmgitems\",\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  className: \"cmg-soon\",\n                  sx: {\n                    textAlign: 'start',\n                    opacity: 1\n                  },\n                  children: translate('Coming soon')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 17\n                }, this), menuItems.map(({\n                  key,\n                  icon,\n                  title,\n                  disabled,\n                  tooltip\n                }, index) => disabled && /*#__PURE__*/_jsxDEV(React.Fragment, {\n                  children: /*#__PURE__*/_jsxDEV(CursorTooltip, {\n                    title: tooltip,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: /*#__PURE__*/_jsxDEV(ButtonBase, {\n                        className: `qadpt-sm-item ${activeItem === key ? 'active' : 'inactive'} ${disabled ? 'disabled' : ''}`,\n                        onClick: () => MenuClick(key),\n                        disabled: disabled,\n                        disableRipple: true,\n                        children: /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            textAlign: 'left',\n                            width: '100%'\n                          },\n                          children: disabled && /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              display: 'flex',\n                              alignItems: 'center',\n                              opacity: disabled ? 0.5 : 1,\n                              '& p': {\n                                fontSize: '14px'\n                              }\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                              className: `qadpt-sm-icon ${activeItem === key ? 'active' : 'inactive'}`,\n                              sx: {\n                                ...(key === 'Banners' && {\n                                  marginLeft: '-3px'\n                                })\n                              },\n                              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                                src: icon,\n                                alt: key\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 342,\n                                columnNumber: 29\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 336,\n                              columnNumber: 27\n                            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                              children: translatedTitles[title] || title\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 345,\n                              columnNumber: 27\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 326,\n                            columnNumber: 25\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 324,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 318,\n                        columnNumber: 21\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 317,\n                      columnNumber: 19\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 17\n                  }, this)\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 15\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 9\n        }, this), openPopup && /*#__PURE__*/_jsxDEV(Popup, {\n          onClose: () => setOpenPopup(false),\n          onOk: () => setOpenPopup(false),\n          type: \"Apply\",\n          title: translate(\"Log out from QuickAdopt\"),\n          description: translate(\"Do you really want to logout?\"),\n          button1: translate(\"Cancel\"),\n          button2: translate(\"Logout\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 243,\n    columnNumber: 5\n  }, this);\n};\n_s2(SideMenu, \"uB6z3DXDvsZkGwyYwH5pftoV3Jg=\", false, function () {\n  return [useTranslation, useAuth, useNavigate, useLocation, useRtl];\n});\n_c2 = SideMenu;\nexport default SideMenu;\nvar _c, _c2;\n$RefreshReg$(_c, \"CursorTooltip\");\n$RefreshReg$(_c2, \"SideMenu\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dashboard", "Audience", "Tours", "Announcements", "Tooltips", "webbanner", "Checklist", "Surveys", "Hotspots", "Settings", "Filemanagement", "useNavigate", "useLocation", "ThemeProvider", "createTheme", "CssBaseline", "Drawer", "List", "ListItemIcon", "Box", "Typography", "Popup", "ButtonBase", "isSidebarOpen", "setSidebarOpen", "subscribe", "PerfectScrollbar", "useAuth", "useTranslation", "useRtl", "jsxDEV", "_jsxDEV", "CursorTooltip", "title", "children", "_s", "isRtl", "visible", "setVisible", "showTooltip", "setShowTooltip", "position", "setPosition", "x", "y", "timerRef", "useRef", "handleMouseEnter", "current", "window", "setTimeout", "document", "addEventListener", "handleMouseMove", "handleMouseLeave", "clearTimeout", "removeEventListener", "e", "gap", "clientX", "clientY", "onMouseEnter", "onMouseLeave", "style", "right", "innerWidth", "left", "top", "boxShadow", "backgroundColor", "color", "fontSize", "borderRadius", "padding", "borderTop", "zIndex", "pointerEvents", "opacity", "width", "transition", "overflow", "transform", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "SideMenu", "selectedLanguageProp", "_s2", "_userDetails$UserType", "t", "translate", "activeItem", "setActiveItem", "openPopup", "set<PERSON>penPop<PERSON>", "sidebarOpen", "setLocalSidebarOpen", "translated<PERSON><PERSON><PERSON>", "setTranslatedTitles", "signOut", "userDetails", "OrganizationId", "setOrganizationId", "user", "setUser", "navigate", "location", "userType", "setUserType", "UserType", "themeWithDirection", "direction", "userInfoString", "localStorage", "getItem", "userInfo", "JSON", "parse", "parsedUser", "_parsedUser$Organizat", "OrgId", "error", "console", "_userDetails$Organiza", "unsubscribe", "MenuClick", "key", "_parsedUser$Organizat2", "toLowerCase", "toLocaleLowerCase", "toggleSidebar", "pathname", "menuItems", "icon", "tooltip", "access", "disabled", "theme", "className", "sx", "display", "variant", "anchor", "open", "map", "index", "includes", "Fragment", "onClick", "disable<PERSON><PERSON><PERSON>", "textAlign", "alignItems", "marginLeft", "src", "alt", "onClose", "onOk", "type", "description", "button1", "button2", "_c2", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptWeb/src/components/adminMenu/sideMenu.tsx"], "sourcesContent": ["import React, { useState, useEffect, useTransition, ReactNode } from \"react\";\r\nimport {\r\n  Dashboard,\r\n  Audience,\r\n  Tours,\r\n  Announcements,\r\n  Tooltips,\r\n  Banners,\r\n  webbanner,\r\n  Checklist,\r\n  Surveys,\r\n  Hotspots,\r\n  Settings,\r\n  Auditlog,\r\n  Filemanagement,\r\n  Cursor\r\n} from \"../../assets/icons/icons\";\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { useTheme, ThemeProvider, createTheme } from \"@mui/material/styles\";\r\nimport CssBaseline from \"@mui/material/CssBaseline\";\r\nimport Drawer from \"@mui/material/Drawer\";\r\nimport List from \"@mui/material/List\";\r\nimport ListItemIcon from \"@mui/material/ListItemIcon\";\r\nimport IconButton from \"@mui/material/IconButton\";\r\nimport Switch from \"@mui/material/Switch\";\r\nimport Box from \"@mui/material/Box\";\r\nimport MenuIcon from \"@mui/icons-material/Menu\";\r\nimport Typography from \"@mui/material/Typography\";\r\nimport Popup from \"../common/Popup\";\r\nimport ButtonBase from '@mui/material/ButtonBase';\r\nimport { isSidebarOpen, setSidebarOpen, subscribe } from \"../adminMenu/sidemenustate\";\r\n// import { useRtl } from \"../../RtlContext\";\r\nimport { Divider, Tooltip as MuiTooltip } from \"@mui/material\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\nimport { useAuth } from \"../auth/AuthProvider\";\r\nimport { User } from \"../../models/User\";\r\nimport { useTranslation } from \"react-i18next\";\r\nimport { useRtl } from \"../../RtlContext\";\r\n\r\n\r\ninterface CursorTooltipProps {\r\n  title: string;\r\n  children: ReactNode;\r\n}\r\n\r\nconst CursorTooltip: React.FC<CursorTooltipProps> = ({ title, children }) => {\r\n  const { isRtl } = useRtl();\r\n  const [visible, setVisible] = useState<boolean>(false);\r\n  const [showTooltip, setShowTooltip] = useState<boolean>(false);\r\n  const [position, setPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });\r\n  const timerRef = React.useRef<number | null>(null);\r\n\r\n  const handleMouseEnter = (): void => {\r\n    timerRef.current = window.setTimeout(() => {\r\n      setShowTooltip(true);\r\n      setVisible(true);\r\n      document.addEventListener('mousemove', handleMouseMove);\r\n    }, 600); \r\n  };\r\n\r\n  const handleMouseLeave = (): void => {\r\n    if (timerRef.current) {\r\n      clearTimeout(timerRef.current);\r\n      timerRef.current = null;\r\n    }\r\n    setVisible(false);\r\n    setShowTooltip(false);\r\n    document.removeEventListener('mousemove', handleMouseMove);\r\n  };\r\n\r\n  const handleMouseMove = (e: MouseEvent): void => {\r\n    const gap = 15;\r\n    setPosition({\r\n      x: e.clientX + gap,\r\n      y: e.clientY + gap\r\n    });\r\n  };\r\n\r\n  return (\r\n    <span onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>\r\n      {children}\r\n      {showTooltip && (\r\n        <div\r\n          style={{\r\n            position: 'fixed',\r\n            ...(isRtl ? { right: `${window.innerWidth - position.x}px` } : { left: `${position.x}px` }),\r\n            top: `${position.y}px`,\r\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\r\n            backgroundColor: '#fff',\r\n            color: '#000',\r\n            fontSize: '12px',\r\n            borderRadius: '8px',\r\n            padding: '16px',\r\n            borderTop: '2px solid #5c80ac',\r\n            zIndex: 9999,\r\n            pointerEvents: 'none',\r\n            opacity: visible ? 1 : 0,\r\n            width: visible ? '248px' : '0px',\r\n            transition: 'all 0.5s ease',\r\n            overflow: 'hidden',\r\n            transform: visible ? 'scale(1)' : 'scale(0.9)',\r\n          }}\r\n        >\r\n          {title}\r\n        </div>\r\n      )}\r\n    </span>\r\n  );\r\n};\r\n\r\ninterface SideMenuProps {\r\n  selectedLanguageProp: string;\r\n}\r\n\r\nconst SideMenu: React.FC<SideMenuProps> = ({ selectedLanguageProp }) => {\r\n  const { t: translate } = useTranslation();\r\n  const [activeItem, setActiveItem] = useState<string>(\"\");\r\n  const [openPopup, setOpenPopup] = useState(false);\r\n  const [sidebarOpen, setLocalSidebarOpen] = useState(isSidebarOpen());\r\n  const [translatedTitles, setTranslatedTitles] = useState<{ [key: string]: string }>({});\r\n  const { signOut, userDetails } = useAuth();\r\n  const [OrganizationId, setOrganizationId] = useState(userDetails?.OrganizationId);\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [userType, setUserType] = useState(userDetails?.UserType ?? \"\");\r\n\r\n  const { isRtl } = useRtl();\r\n\r\n  const themeWithDirection = createTheme({\r\n    direction: isRtl ? \"rtl\" : \"ltr\",\r\n  });\r\n\r\n  useEffect(() => {\r\n    const userInfoString = localStorage.getItem(\"userInfo\");  \r\n    if (userInfoString) { \r\n      try {\r\n        const userInfo = JSON.parse(userInfoString);  \r\n        if (userInfo['user']) {\r\n          const parsedUser = JSON.parse(userInfo['user']);\r\n          setUser(parsedUser);  \r\n          if (parsedUser) {\r\n            const OrgId = parsedUser.OrganizationId ?? '';\r\n            setOrganizationId(OrgId);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error parsing userInfo: \", error);\r\n      }\r\n    }\r\n    else if (userDetails) {\r\n      setUser(userDetails);  \r\n      if (userDetails) {\r\n        const OrgId = userDetails.OrganizationId ?? '';\r\n        setOrganizationId(OrgId);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const unsubscribe = subscribe(setLocalSidebarOpen);\r\n    return () => unsubscribe();\r\n  }, []);\r\n\r\n  const MenuClick = (key: string) => {    \r\n    if (!OrganizationId)\r\n    {\r\n      const userInfoString = localStorage.getItem(\"userInfo\");  \r\n      if (userInfoString) { \r\n        try {\r\n          const userInfo = JSON.parse(userInfoString);  \r\n          if (userInfo['user']) {\r\n            const parsedUser = JSON.parse(userInfo['user']);\r\n            setUser(parsedUser);  \r\n            if (parsedUser) {\r\n              const OrgId = parsedUser.OrganizationId ?? '';\r\n              setOrganizationId(OrgId);\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error parsing userInfo: \", error);\r\n        }\r\n      }\r\n    }\r\n    \r\n    setActiveItem(key);\r\n    switch (key.toLowerCase()) {\r\n      case \"dashboard\":\r\n        return navigate(\"/dashboard\");\r\n      case \"audience\":\r\n        return navigate(\"/audience\");\r\n      case \"tours\":\r\n        return navigate(\"/tours\");\r\n      case \"announcements\":\r\n        return navigate(\"/announcements\");\r\n      case \"tooltips\":\r\n        return navigate(\"/tooltips\");\r\n      case \"banners\":\r\n        return navigate(\"/banners\");\r\n      case \"checklists\":\r\n        return navigate(\"/checklists\");\r\n      case \"surveys\":\r\n        return navigate(\"/surveys\");\r\n      case \"hotspots\":\r\n        return navigate(\"/hotspots\");\r\n      case \"settings\":\r\n        if(userType.toLocaleLowerCase() == \"admin\")\r\n          return navigate(`${OrganizationId}/team`); \r\n        else\r\n          return navigate(`settings/rights`); \r\n      case 'filemanagement':\r\n        return navigate(\"/filelist\");\r\n      default:\r\n        return navigate(\"/\");\r\n    }\r\n  };\r\n\r\n  const toggleSidebar = () => {\r\n    setLocalSidebarOpen(!sidebarOpen);\r\n    setSidebarOpen(!sidebarOpen);\r\n  };\r\n\r\n  if (location.pathname === \"/Builder\") {\r\n    return null;\r\n  }\r\n\r\n  const menuItems = [\r\n    { key: \"Announcements\", icon: Announcements, title: translate('Announcements'), tooltip: translate('Helps to communicate important updates, notifications, or messages.'), access: ['admin,user'] },\r\n    { key: \"Banners\", icon: webbanner, title: translate('Banners'), tooltip: translate('Displays notifications at the top of the screen.'), access: ['admin,user'] },\r\n    { key: \"Tooltips\", icon: Tooltips, title: translate('Tooltips'), tooltip: translate('Provide quick explanations, tips, or instructions of the tools,'), access: ['admin,user'] },\r\n    { key: \"Hotspots\", icon: Hotspots, title: translate('Hotspots'), tooltip: translate('Interactive areas to draw attention to important features, actions, or guidance.'), access: ['admin,user'] },\r\n    { key: \"Tours\", icon: Tours, title: translate('Product Tour'), tooltip: translate('Step-by-step guides to navigate and understand key features.'), access: ['admin,user'] },\r\n    { key: \"Checklists\", icon: Checklist, title: translate('Checklists'), tooltip: translate('Task lists that guide users through a series of steps or actions.'), access: ['admin,user'] },\r\n    { key: \"Filemanagement\", icon: Filemanagement, title: translate('File Management'), tooltip: translate('Store and reuse media assets across multiple interactions.'), access: ['user'] },\r\n    { key: \"Settings\", icon: Settings, title: translate('Settings'), tooltip: translate('Configure application settings and preferences.'), access: ['admin,user'] },\r\n    { key: \"Surveys\", icon: Surveys, title: translate('Surveys'), disabled: true, tooltip: translate('Interactive forms or questionnaires designed to collect feedback, insights, or opinions'), access: ['admin,user'] },\r\n    { key: \"Dashboard\", icon: Dashboard, title: translate('Dashboard'), disabled: true, tooltip: translate('Get a unified view of insights and actions in one place.'), access: ['admin,user'] },\r\n    { key: \"Audience\", icon: Audience, title: translate('Audience'), disabled: true, tooltip: translate('Categorize users into segments for precise targeting.'), access: ['admin,user'] },\r\n  ];\r\n  \r\n  return (\r\n    <ThemeProvider theme={themeWithDirection}>\r\n      <CssBaseline />\r\n      <div className=\"qadpt-side-menu\">\r\n      <Box sx={{ display: \"flex\" }}>\r\n        <Drawer\r\n          variant=\"persistent\"\r\n          anchor={isRtl ? \"right\" : \"left\"}\r\n          open={sidebarOpen}\r\n          className=\"qadpt-smenu\"\r\n          sx={{\r\n            \"& .MuiDrawer-paper\": {\r\n              direction: isRtl ? \"rtl\" : \"ltr\",\r\n            },\r\n          }}\r\n        >\r\n          <PerfectScrollbar>\r\n              <List className=\"qadpt-smenu-list\">\r\n              \r\n                {menuItems.map(({ key, icon, title, disabled, tooltip,access }, index) => (\r\n              !disabled && !access.includes(userType.toLocaleLowerCase()) &&\r\n              <React.Fragment key={key}>\r\n                <CursorTooltip title={tooltip}>\r\n                  <span>\r\n                    <ButtonBase \r\n                      className={`qadpt-sm-item ${activeItem === key ? 'active' : 'inactive'} ${disabled ? 'disabled' : ''}`}\r\n                      onClick={() => MenuClick(key)}\r\n\t\t\t\t\t\t\t\tdisabled={disabled}\r\n\t\t\t\t\t\t\t\tdisableRipple\r\n                    >\r\n                      <Box sx={{ textAlign: 'left', width: '100%' }}>\r\n                        {!disabled&&(\r\n                        <Box\r\n                          sx={{\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            opacity: disabled ? 0.5 : 1,\r\n                            '& p': {\r\n                              fontSize: '14px', \r\n                            },    \r\n                          }}\r\n                        >\r\n                          <ListItemIcon\r\n                            className={`qadpt-sm-icon ${activeItem === key ? 'active' : 'inactive'}`}\r\n                            sx={{\r\n                              ...(key === 'Banners' && { marginLeft: '-3px' }),\r\n                            }}\r\n                          >\r\n                            <img src={icon} alt={key} />\r\n                          </ListItemIcon>\r\n\r\n                          <Typography className=\"qadpt-sm-text\">\r\n                            {translatedTitles[title] || title}\r\n                          </Typography>\r\n                        </Box>)}\r\n                      </Box>\r\n                    </ButtonBase>\r\n                  </span>\r\n                </CursorTooltip>\r\n              </React.Fragment>\r\n                ))}\r\n                <Box className = \"qadpt-cmgitems\">\r\n                <Typography\r\n                            className=\"cmg-soon\"\r\n                            sx={{\r\n                              textAlign: 'start',\r\n                              opacity: 1, \r\n                            }}\r\n                          >\r\n                    {translate('Coming soon')}\r\n                  </Typography>\r\n                {menuItems.map(({ key, icon, title, disabled, tooltip }, index) => (\r\n              disabled &&\r\n              <React.Fragment key={key}>\r\n                <CursorTooltip title={tooltip}>\r\n                  <span>\r\n                    <ButtonBase \r\n                      className={`qadpt-sm-item ${activeItem === key ? 'active' : 'inactive'} ${disabled ? 'disabled' : ''}`}\r\n                      onClick={() => MenuClick(key)}\r\n\t\t\t\t\t\t\t\tdisabled={disabled}\r\n\t\t\t\t\t\t\t\tdisableRipple\r\n                    >\r\n                      <Box sx={{ textAlign: 'left', width: '100%' }}>\r\n                        {disabled && (\r\n                        <Box\r\n                          sx={{\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            opacity: disabled ? 0.5 : 1,\r\n                            '& p': {\r\n                              fontSize: '14px', \r\n                            },    \r\n                          }}\r\n                        >\r\n                          <ListItemIcon\r\n                            className={`qadpt-sm-icon ${activeItem === key ? 'active' : 'inactive'}`}\r\n                            sx={{\r\n                              ...(key === 'Banners' && { marginLeft: '-3px' }),\r\n                            }}\r\n                          >\r\n                            <img src={icon} alt={key} />\r\n                          </ListItemIcon>\r\n\r\n                          <Typography>\r\n                            {translatedTitles[title] || title}\r\n                          </Typography>\r\n                        </Box>)}\r\n                      </Box>\r\n                    </ButtonBase>\r\n                  </span>\r\n                </CursorTooltip>\r\n              </React.Fragment>\r\n                ))}\r\n                </Box>\r\n          </List>\r\n          </PerfectScrollbar>  \r\n        </Drawer>\r\n        \r\n        {openPopup && (\r\n          <Popup\r\n            onClose={() => setOpenPopup(false)}\r\n            onOk={() => setOpenPopup(false)}\r\n            type=\"Apply\"\r\n            title={translate(\"Log out from QuickAdopt\")}\r\n            description={translate(\"Do you really want to logout?\")}\r\n            button1={translate(\"Cancel\")}\r\n            button2={translate(\"Logout\")}\r\n          />\r\n        )}\r\n      </Box>\r\n      </div>\r\n    </ThemeProvider>\r\n  );\r\n};\r\n\r\nexport default SideMenu;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAkC,OAAO;AAC5E,SACEC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,aAAa,EACbC,QAAQ,EAERC,SAAS,EACTC,SAAS,EACTC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EAERC,cAAc,QAET,0BAA0B;AACjC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAAmBC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AAC3E,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,YAAY,MAAM,4BAA4B;AAGrD,OAAOC,GAAG,MAAM,mBAAmB;AAEnC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,aAAa,EAAEC,cAAc,EAAEC,SAAS,QAAQ,4BAA4B;AACrF;;AAEA,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,OAAO,6CAA6C;AACpD,SAASC,OAAO,QAAQ,sBAAsB;AAE9C,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,MAAM,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ1C,MAAMC,aAA2C,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM;IAAEC;EAAM,CAAC,GAAGP,MAAM,CAAC,CAAC;EAC1B,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAU,KAAK,CAAC;EAC9D,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAA2B;IAAE6C,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAClF,MAAMC,QAAQ,GAAGhD,KAAK,CAACiD,MAAM,CAAgB,IAAI,CAAC;EAElD,MAAMC,gBAAgB,GAAGA,CAAA,KAAY;IACnCF,QAAQ,CAACG,OAAO,GAAGC,MAAM,CAACC,UAAU,CAAC,MAAM;MACzCV,cAAc,CAAC,IAAI,CAAC;MACpBF,UAAU,CAAC,IAAI,CAAC;MAChBa,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEC,eAAe,CAAC;IACzD,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAY;IACnC,IAAIT,QAAQ,CAACG,OAAO,EAAE;MACpBO,YAAY,CAACV,QAAQ,CAACG,OAAO,CAAC;MAC9BH,QAAQ,CAACG,OAAO,GAAG,IAAI;IACzB;IACAV,UAAU,CAAC,KAAK,CAAC;IACjBE,cAAc,CAAC,KAAK,CAAC;IACrBW,QAAQ,CAACK,mBAAmB,CAAC,WAAW,EAAEH,eAAe,CAAC;EAC5D,CAAC;EAED,MAAMA,eAAe,GAAII,CAAa,IAAW;IAC/C,MAAMC,GAAG,GAAG,EAAE;IACdhB,WAAW,CAAC;MACVC,CAAC,EAAEc,CAAC,CAACE,OAAO,GAAGD,GAAG;MAClBd,CAAC,EAAEa,CAAC,CAACG,OAAO,GAAGF;IACjB,CAAC,CAAC;EACJ,CAAC;EAED,oBACE3B,OAAA;IAAM8B,YAAY,EAAEd,gBAAiB;IAACe,YAAY,EAAER,gBAAiB;IAAApB,QAAA,GAClEA,QAAQ,EACRK,WAAW,iBACVR,OAAA;MACEgC,KAAK,EAAE;QACLtB,QAAQ,EAAE,OAAO;QACjB,IAAIL,KAAK,GAAG;UAAE4B,KAAK,EAAE,GAAGf,MAAM,CAACgB,UAAU,GAAGxB,QAAQ,CAACE,CAAC;QAAK,CAAC,GAAG;UAAEuB,IAAI,EAAE,GAAGzB,QAAQ,CAACE,CAAC;QAAK,CAAC,CAAC;QAC3FwB,GAAG,EAAE,GAAG1B,QAAQ,CAACG,CAAC,IAAI;QACtBwB,SAAS,EAAE,2BAA2B;QACtCC,eAAe,EAAE,MAAM;QACvBC,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,MAAM;QAChBC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE,mBAAmB;QAC9BC,MAAM,EAAE,IAAI;QACZC,aAAa,EAAE,MAAM;QACrBC,OAAO,EAAExC,OAAO,GAAG,CAAC,GAAG,CAAC;QACxByC,KAAK,EAAEzC,OAAO,GAAG,OAAO,GAAG,KAAK;QAChC0C,UAAU,EAAE,eAAe;QAC3BC,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE5C,OAAO,GAAG,UAAU,GAAG;MACpC,CAAE;MAAAH,QAAA,EAEDD;IAAK;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAAClD,EAAA,CA/DIH,aAA2C;EAAA,QAC7BH,MAAM;AAAA;AAAAyD,EAAA,GADpBtD,aAA2C;AAqEjD,MAAMuD,QAAiC,GAAGA,CAAC;EAAEC;AAAqB,CAAC,KAAK;EAAAC,GAAA;EAAA,IAAAC,qBAAA;EACtE,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGhE,cAAc,CAAC,CAAC;EACzC,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGhG,QAAQ,CAAS,EAAE,CAAC;EACxD,MAAM,CAACiG,SAAS,EAAEC,YAAY,CAAC,GAAGlG,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmG,WAAW,EAAEC,mBAAmB,CAAC,GAAGpG,QAAQ,CAACyB,aAAa,CAAC,CAAC,CAAC;EACpE,MAAM,CAAC4E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtG,QAAQ,CAA4B,CAAC,CAAC,CAAC;EACvF,MAAM;IAAEuG,OAAO;IAAEC;EAAY,CAAC,GAAG3E,OAAO,CAAC,CAAC;EAC1C,MAAM,CAAC4E,cAAc,EAAEC,iBAAiB,CAAC,GAAG1G,QAAQ,CAACwG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEC,cAAc,CAAC;EACjF,MAAM,CAACE,IAAI,EAAEC,OAAO,CAAC,GAAG5G,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM6G,QAAQ,GAAGhG,WAAW,CAAC,CAAC;EAC9B,MAAMiG,QAAQ,GAAGhG,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiG,QAAQ,EAAEC,WAAW,CAAC,GAAGhH,QAAQ,EAAA4F,qBAAA,GAACY,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAES,QAAQ,cAAArB,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;EAErE,MAAM;IAAEtD;EAAM,CAAC,GAAGP,MAAM,CAAC,CAAC;EAE1B,MAAMmF,kBAAkB,GAAGlG,WAAW,CAAC;IACrCmG,SAAS,EAAE7E,KAAK,GAAG,KAAK,GAAG;EAC7B,CAAC,CAAC;EAEFrC,SAAS,CAAC,MAAM;IACd,MAAMmH,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACvD,IAAIF,cAAc,EAAE;MAClB,IAAI;QACF,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC;QAC3C,IAAIG,QAAQ,CAAC,MAAM,CAAC,EAAE;UACpB,MAAMG,UAAU,GAAGF,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC;UAC/CX,OAAO,CAACc,UAAU,CAAC;UACnB,IAAIA,UAAU,EAAE;YAAA,IAAAC,qBAAA;YACd,MAAMC,KAAK,IAAAD,qBAAA,GAAGD,UAAU,CAACjB,cAAc,cAAAkB,qBAAA,cAAAA,qBAAA,GAAI,EAAE;YAC7CjB,iBAAiB,CAACkB,KAAK,CAAC;UAC1B;QACF;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;IACF,CAAC,MACI,IAAIrB,WAAW,EAAE;MACpBI,OAAO,CAACJ,WAAW,CAAC;MACpB,IAAIA,WAAW,EAAE;QAAA,IAAAuB,qBAAA;QACf,MAAMH,KAAK,IAAAG,qBAAA,GAAGvB,WAAW,CAACC,cAAc,cAAAsB,qBAAA,cAAAA,qBAAA,GAAI,EAAE;QAC9CrB,iBAAiB,CAACkB,KAAK,CAAC;MAC1B;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EAEN3H,SAAS,CAAC,MAAM;IACd,MAAM+H,WAAW,GAAGrG,SAAS,CAACyE,mBAAmB,CAAC;IAClD,OAAO,MAAM4B,WAAW,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,SAAS,GAAIC,GAAW,IAAK;IACjC,IAAI,CAACzB,cAAc,EACnB;MACE,MAAMW,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MACvD,IAAIF,cAAc,EAAE;QAClB,IAAI;UACF,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,cAAc,CAAC;UAC3C,IAAIG,QAAQ,CAAC,MAAM,CAAC,EAAE;YACpB,MAAMG,UAAU,GAAGF,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC/CX,OAAO,CAACc,UAAU,CAAC;YACnB,IAAIA,UAAU,EAAE;cAAA,IAAAS,sBAAA;cACd,MAAMP,KAAK,IAAAO,sBAAA,GAAGT,UAAU,CAACjB,cAAc,cAAA0B,sBAAA,cAAAA,sBAAA,GAAI,EAAE;cAC7CzB,iBAAiB,CAACkB,KAAK,CAAC;YAC1B;UACF;QACF,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;MACF;IACF;IAEA7B,aAAa,CAACkC,GAAG,CAAC;IAClB,QAAQA,GAAG,CAACE,WAAW,CAAC,CAAC;MACvB,KAAK,WAAW;QACd,OAAOvB,QAAQ,CAAC,YAAY,CAAC;MAC/B,KAAK,UAAU;QACb,OAAOA,QAAQ,CAAC,WAAW,CAAC;MAC9B,KAAK,OAAO;QACV,OAAOA,QAAQ,CAAC,QAAQ,CAAC;MAC3B,KAAK,eAAe;QAClB,OAAOA,QAAQ,CAAC,gBAAgB,CAAC;MACnC,KAAK,UAAU;QACb,OAAOA,QAAQ,CAAC,WAAW,CAAC;MAC9B,KAAK,SAAS;QACZ,OAAOA,QAAQ,CAAC,UAAU,CAAC;MAC7B,KAAK,YAAY;QACf,OAAOA,QAAQ,CAAC,aAAa,CAAC;MAChC,KAAK,SAAS;QACZ,OAAOA,QAAQ,CAAC,UAAU,CAAC;MAC7B,KAAK,UAAU;QACb,OAAOA,QAAQ,CAAC,WAAW,CAAC;MAC9B,KAAK,UAAU;QACb,IAAGE,QAAQ,CAACsB,iBAAiB,CAAC,CAAC,IAAI,OAAO,EACxC,OAAOxB,QAAQ,CAAC,GAAGJ,cAAc,OAAO,CAAC,CAAC,KAE1C,OAAOI,QAAQ,CAAC,iBAAiB,CAAC;MACtC,KAAK,gBAAgB;QACnB,OAAOA,QAAQ,CAAC,WAAW,CAAC;MAC9B;QACE,OAAOA,QAAQ,CAAC,GAAG,CAAC;IACxB;EACF,CAAC;EAED,MAAMyB,aAAa,GAAGA,CAAA,KAAM;IAC1BlC,mBAAmB,CAAC,CAACD,WAAW,CAAC;IACjCzE,cAAc,CAAC,CAACyE,WAAW,CAAC;EAC9B,CAAC;EAED,IAAIW,QAAQ,CAACyB,QAAQ,KAAK,UAAU,EAAE;IACpC,OAAO,IAAI;EACb;EAEA,MAAMC,SAAS,GAAG,CAChB;IAAEN,GAAG,EAAE,eAAe;IAAEO,IAAI,EAAEpI,aAAa;IAAE8B,KAAK,EAAE2D,SAAS,CAAC,eAAe,CAAC;IAAE4C,OAAO,EAAE5C,SAAS,CAAC,qEAAqE,CAAC;IAAE6C,MAAM,EAAE,CAAC,YAAY;EAAE,CAAC,EACnM;IAAET,GAAG,EAAE,SAAS;IAAEO,IAAI,EAAElI,SAAS;IAAE4B,KAAK,EAAE2D,SAAS,CAAC,SAAS,CAAC;IAAE4C,OAAO,EAAE5C,SAAS,CAAC,kDAAkD,CAAC;IAAE6C,MAAM,EAAE,CAAC,YAAY;EAAE,CAAC,EAChK;IAAET,GAAG,EAAE,UAAU;IAAEO,IAAI,EAAEnI,QAAQ;IAAE6B,KAAK,EAAE2D,SAAS,CAAC,UAAU,CAAC;IAAE4C,OAAO,EAAE5C,SAAS,CAAC,iEAAiE,CAAC;IAAE6C,MAAM,EAAE,CAAC,YAAY;EAAE,CAAC,EAChL;IAAET,GAAG,EAAE,UAAU;IAAEO,IAAI,EAAE/H,QAAQ;IAAEyB,KAAK,EAAE2D,SAAS,CAAC,UAAU,CAAC;IAAE4C,OAAO,EAAE5C,SAAS,CAAC,kFAAkF,CAAC;IAAE6C,MAAM,EAAE,CAAC,YAAY;EAAE,CAAC,EACjM;IAAET,GAAG,EAAE,OAAO;IAAEO,IAAI,EAAErI,KAAK;IAAE+B,KAAK,EAAE2D,SAAS,CAAC,cAAc,CAAC;IAAE4C,OAAO,EAAE5C,SAAS,CAAC,8DAA8D,CAAC;IAAE6C,MAAM,EAAE,CAAC,YAAY;EAAE,CAAC,EAC3K;IAAET,GAAG,EAAE,YAAY;IAAEO,IAAI,EAAEjI,SAAS;IAAE2B,KAAK,EAAE2D,SAAS,CAAC,YAAY,CAAC;IAAE4C,OAAO,EAAE5C,SAAS,CAAC,mEAAmE,CAAC;IAAE6C,MAAM,EAAE,CAAC,YAAY;EAAE,CAAC,EACvL;IAAET,GAAG,EAAE,gBAAgB;IAAEO,IAAI,EAAE7H,cAAc;IAAEuB,KAAK,EAAE2D,SAAS,CAAC,iBAAiB,CAAC;IAAE4C,OAAO,EAAE5C,SAAS,CAAC,4DAA4D,CAAC;IAAE6C,MAAM,EAAE,CAAC,MAAM;EAAE,CAAC,EACxL;IAAET,GAAG,EAAE,UAAU;IAAEO,IAAI,EAAE9H,QAAQ;IAAEwB,KAAK,EAAE2D,SAAS,CAAC,UAAU,CAAC;IAAE4C,OAAO,EAAE5C,SAAS,CAAC,iDAAiD,CAAC;IAAE6C,MAAM,EAAE,CAAC,YAAY;EAAE,CAAC,EAChK;IAAET,GAAG,EAAE,SAAS;IAAEO,IAAI,EAAEhI,OAAO;IAAE0B,KAAK,EAAE2D,SAAS,CAAC,SAAS,CAAC;IAAE8C,QAAQ,EAAE,IAAI;IAAEF,OAAO,EAAE5C,SAAS,CAAC,yFAAyF,CAAC;IAAE6C,MAAM,EAAE,CAAC,YAAY;EAAE,CAAC,EACrN;IAAET,GAAG,EAAE,WAAW;IAAEO,IAAI,EAAEvI,SAAS;IAAEiC,KAAK,EAAE2D,SAAS,CAAC,WAAW,CAAC;IAAE8C,QAAQ,EAAE,IAAI;IAAEF,OAAO,EAAE5C,SAAS,CAAC,0DAA0D,CAAC;IAAE6C,MAAM,EAAE,CAAC,YAAY;EAAE,CAAC,EAC5L;IAAET,GAAG,EAAE,UAAU;IAAEO,IAAI,EAAEtI,QAAQ;IAAEgC,KAAK,EAAE2D,SAAS,CAAC,UAAU,CAAC;IAAE8C,QAAQ,EAAE,IAAI;IAAEF,OAAO,EAAE5C,SAAS,CAAC,uDAAuD,CAAC;IAAE6C,MAAM,EAAE,CAAC,YAAY;EAAE,CAAC,CACvL;EAED,oBACE1G,OAAA,CAAClB,aAAa;IAAC8H,KAAK,EAAE3B,kBAAmB;IAAA9E,QAAA,gBACvCH,OAAA,CAAChB,WAAW;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACftD,OAAA;MAAK6G,SAAS,EAAC,iBAAiB;MAAA1G,QAAA,eAChCH,OAAA,CAACZ,GAAG;QAAC0H,EAAE,EAAE;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAA5G,QAAA,gBAC3BH,OAAA,CAACf,MAAM;UACL+H,OAAO,EAAC,YAAY;UACpBC,MAAM,EAAE5G,KAAK,GAAG,OAAO,GAAG,MAAO;UACjC6G,IAAI,EAAEhD,WAAY;UAClB2C,SAAS,EAAC,aAAa;UACvBC,EAAE,EAAE;YACF,oBAAoB,EAAE;cACpB5B,SAAS,EAAE7E,KAAK,GAAG,KAAK,GAAG;YAC7B;UACF,CAAE;UAAAF,QAAA,eAEFH,OAAA,CAACL,gBAAgB;YAAAQ,QAAA,eACbH,OAAA,CAACd,IAAI;cAAC2H,SAAS,EAAC,kBAAkB;cAAA1G,QAAA,GAE/BoG,SAAS,CAACY,GAAG,CAAC,CAAC;gBAAElB,GAAG;gBAAEO,IAAI;gBAAEtG,KAAK;gBAAEyG,QAAQ;gBAAEF,OAAO;gBAACC;cAAO,CAAC,EAAEU,KAAK,KACvE,CAACT,QAAQ,IAAI,CAACD,MAAM,CAACW,QAAQ,CAACvC,QAAQ,CAACsB,iBAAiB,CAAC,CAAC,CAAC,iBAC3DpG,OAAA,CAAClC,KAAK,CAACwJ,QAAQ;gBAAAnH,QAAA,eACbH,OAAA,CAACC,aAAa;kBAACC,KAAK,EAAEuG,OAAQ;kBAAAtG,QAAA,eAC5BH,OAAA;oBAAAG,QAAA,eACEH,OAAA,CAACT,UAAU;sBACTsH,SAAS,EAAE,iBAAiB/C,UAAU,KAAKmC,GAAG,GAAG,QAAQ,GAAG,UAAU,IAAIU,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;sBACvGY,OAAO,EAAEA,CAAA,KAAMvB,SAAS,CAACC,GAAG,CAAE;sBAC5CU,QAAQ,EAAEA,QAAS;sBACnBa,aAAa;sBAAArH,QAAA,eAECH,OAAA,CAACZ,GAAG;wBAAC0H,EAAE,EAAE;0BAAEW,SAAS,EAAE,MAAM;0BAAE1E,KAAK,EAAE;wBAAO,CAAE;wBAAA5C,QAAA,EAC3C,CAACwG,QAAQ,iBACV3G,OAAA,CAACZ,GAAG;0BACF0H,EAAE,EAAE;4BACFC,OAAO,EAAE,MAAM;4BACfW,UAAU,EAAE,QAAQ;4BACpB5E,OAAO,EAAE6D,QAAQ,GAAG,GAAG,GAAG,CAAC;4BAC3B,KAAK,EAAE;8BACLnE,QAAQ,EAAE;4BACZ;0BACF,CAAE;0BAAArC,QAAA,gBAEFH,OAAA,CAACb,YAAY;4BACX0H,SAAS,EAAE,iBAAiB/C,UAAU,KAAKmC,GAAG,GAAG,QAAQ,GAAG,UAAU,EAAG;4BACzEa,EAAE,EAAE;8BACF,IAAIb,GAAG,KAAK,SAAS,IAAI;gCAAE0B,UAAU,EAAE;8BAAO,CAAC;4BACjD,CAAE;4BAAAxH,QAAA,eAEFH,OAAA;8BAAK4H,GAAG,EAAEpB,IAAK;8BAACqB,GAAG,EAAE5B;4BAAI;8BAAA9C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChB,CAAC,eAEftD,OAAA,CAACX,UAAU;4BAACwH,SAAS,EAAC,eAAe;4BAAA1G,QAAA,EAClCiE,gBAAgB,CAAClE,KAAK,CAAC,IAAIA;0BAAK;4BAAAiD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM;cAAC,GArCG2C,GAAG;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsCR,CACb,CAAC,eACFtD,OAAA,CAACZ,GAAG;gBAACyH,SAAS,EAAG,gBAAgB;gBAAA1G,QAAA,gBACjCH,OAAA,CAACX,UAAU;kBACCwH,SAAS,EAAC,UAAU;kBACpBC,EAAE,EAAE;oBACFW,SAAS,EAAE,OAAO;oBAClB3E,OAAO,EAAE;kBACX,CAAE;kBAAA3C,QAAA,EAET0D,SAAS,CAAC,aAAa;gBAAC;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,EACdiD,SAAS,CAACY,GAAG,CAAC,CAAC;kBAAElB,GAAG;kBAAEO,IAAI;kBAAEtG,KAAK;kBAAEyG,QAAQ;kBAAEF;gBAAQ,CAAC,EAAEW,KAAK,KAChET,QAAQ,iBACR3G,OAAA,CAAClC,KAAK,CAACwJ,QAAQ;kBAAAnH,QAAA,eACbH,OAAA,CAACC,aAAa;oBAACC,KAAK,EAAEuG,OAAQ;oBAAAtG,QAAA,eAC5BH,OAAA;sBAAAG,QAAA,eACEH,OAAA,CAACT,UAAU;wBACTsH,SAAS,EAAE,iBAAiB/C,UAAU,KAAKmC,GAAG,GAAG,QAAQ,GAAG,UAAU,IAAIU,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;wBACvGY,OAAO,EAAEA,CAAA,KAAMvB,SAAS,CAACC,GAAG,CAAE;wBAC5CU,QAAQ,EAAEA,QAAS;wBACnBa,aAAa;wBAAArH,QAAA,eAECH,OAAA,CAACZ,GAAG;0BAAC0H,EAAE,EAAE;4BAAEW,SAAS,EAAE,MAAM;4BAAE1E,KAAK,EAAE;0BAAO,CAAE;0BAAA5C,QAAA,EAC3CwG,QAAQ,iBACT3G,OAAA,CAACZ,GAAG;4BACF0H,EAAE,EAAE;8BACFC,OAAO,EAAE,MAAM;8BACfW,UAAU,EAAE,QAAQ;8BACpB5E,OAAO,EAAE6D,QAAQ,GAAG,GAAG,GAAG,CAAC;8BAC3B,KAAK,EAAE;gCACLnE,QAAQ,EAAE;8BACZ;4BACF,CAAE;4BAAArC,QAAA,gBAEFH,OAAA,CAACb,YAAY;8BACX0H,SAAS,EAAE,iBAAiB/C,UAAU,KAAKmC,GAAG,GAAG,QAAQ,GAAG,UAAU,EAAG;8BACzEa,EAAE,EAAE;gCACF,IAAIb,GAAG,KAAK,SAAS,IAAI;kCAAE0B,UAAU,EAAE;gCAAO,CAAC;8BACjD,CAAE;8BAAAxH,QAAA,eAEFH,OAAA;gCAAK4H,GAAG,EAAEpB,IAAK;gCAACqB,GAAG,EAAE5B;8BAAI;gCAAA9C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChB,CAAC,eAEftD,OAAA,CAACX,UAAU;8BAAAc,QAAA,EACRiE,gBAAgB,CAAClE,KAAK,CAAC,IAAIA;4BAAK;8BAAAiD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvB,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV;wBAAE;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACM;gBAAC,GArCG2C,GAAG;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsCR,CACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,EAERU,SAAS,iBACRhE,OAAA,CAACV,KAAK;UACJwI,OAAO,EAAEA,CAAA,KAAM7D,YAAY,CAAC,KAAK,CAAE;UACnC8D,IAAI,EAAEA,CAAA,KAAM9D,YAAY,CAAC,KAAK,CAAE;UAChC+D,IAAI,EAAC,OAAO;UACZ9H,KAAK,EAAE2D,SAAS,CAAC,yBAAyB,CAAE;UAC5CoE,WAAW,EAAEpE,SAAS,CAAC,+BAA+B,CAAE;UACxDqE,OAAO,EAAErE,SAAS,CAAC,QAAQ,CAAE;UAC7BsE,OAAO,EAAEtE,SAAS,CAAC,QAAQ;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB,CAAC;AAACI,GAAA,CAnQIF,QAAiC;EAAA,QACZ3D,cAAc,EAKND,OAAO,EAGvBhB,WAAW,EACXC,WAAW,EAGViB,MAAM;AAAA;AAAAsI,GAAA,GAbpB5E,QAAiC;AAqQvC,eAAeA,QAAQ;AAAC,IAAAD,EAAA,EAAA6E,GAAA;AAAAC,YAAA,CAAA9E,EAAA;AAAA8E,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}